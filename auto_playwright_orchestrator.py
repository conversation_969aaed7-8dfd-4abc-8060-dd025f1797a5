import os
from typing import Dict, List, Any, Optional
from playwright.sync_api import Page
from llm_service import LLMService
from browser_manager import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from auto_page_object_generator import AutoPageObjectGenerator
from auto_visual_element_finder import AutoVisualElementFinder
from auto_test_generator import <PERSON>Test<PERSON><PERSON>ator
from auto_performance_monitor import AutoPerformanceMonitor
import json
from datetime import datetime


class AutoPlaywrightOrchestrator:
    """
    Main orchestrator for Auto Playwright functionality.
    Integrates all auto features including page object generation,
    visual element finding, test generation, and performance monitoring.
    """

    def __init__(self, api_key: str = None, model: str = "gpt-3.5-turbo", headless: bool = False):
        """
        Initialize the Auto Playwright Orchestrator.

        Args:
            api_key (str, optional): OpenAI API key
            model (str): LLM model to use
            headless (bool): Whether to run browser in headless mode
        """
        self.llm_service = LLMService(api_key=api_key, model=model)
        self.browser_manager = BrowserManager(headless=headless)
        
        # Auto components (initialized after browser launch)
        self.page_object_generator = None
        self.visual_element_finder = None
        self.test_generator = None
        self.performance_monitor = None
        
        # State tracking
        self.browser_initialized = False
        self.current_session = None

    def initialize_browser(self):
        """Initialize browser and all auto components."""
        if self.browser_initialized:
            print("Browser already initialized")
            return
        
        print("Initializing Auto Playwright browser and components...")
        
        # Launch browser
        self.browser_manager.launch_browser()
        page = self.browser_manager.get_page()
        
        # Initialize auto components
        self.page_object_generator = AutoPageObjectGenerator(page)
        self.visual_element_finder = AutoVisualElementFinder(page, self.llm_service)
        self.test_generator = AutoTestGenerator(page, self.llm_service)
        self.performance_monitor = AutoPerformanceMonitor(page)
        
        self.browser_initialized = True
        print("Auto Playwright components initialized successfully")

    def start_auto_session(self, session_name: str = None) -> str:
        """
        Start a comprehensive auto testing session.

        Args:
            session_name (str, optional): Name for the session

        Returns:
            str: Session ID
        """
        if not self.browser_initialized:
            self.initialize_browser()
        
        session_id = session_name or f"auto_session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        self.current_session = {
            "session_id": session_id,
            "start_time": datetime.now().isoformat(),
            "components_active": {
                "test_recording": False,
                "performance_monitoring": False,
                "page_analysis": False
            },
            "results": {
                "page_objects": [],
                "recorded_tests": [],
                "performance_reports": [],
                "analysis_reports": []
            }
        }
        
        print(f"Started Auto Playwright session: {session_id}")
        return session_id

    def analyze_page_and_generate_pom(self, url: str, class_name: str = None) -> Dict[str, Any]:
        """
        Analyze a page and generate a Page Object Model.

        Args:
            url (str): URL to analyze
            class_name (str, optional): Name for the generated class

        Returns:
            Dict[str, Any]: Analysis results and generated POM info
        """
        if not self.browser_initialized:
            self.initialize_browser()
        
        print(f"Analyzing page and generating POM for: {url}")
        
        # Analyze the page
        analysis_results = self.page_object_generator.analyze_page(url)
        
        # Generate Page Object Model
        class_name = class_name or self.page_object_generator._generate_class_name(
            analysis_results['metadata']['title']
        )
        
        # Save the generated class
        pom_file = self.page_object_generator.save_page_object_class(class_name)
        analysis_file = self.page_object_generator.save_analysis_report()
        
        result = {
            "url": url,
            "class_name": class_name,
            "pom_file": pom_file,
            "analysis_file": analysis_file,
            "analysis_results": analysis_results,
            "generated_at": datetime.now().isoformat()
        }
        
        # Add to session results
        if self.current_session:
            self.current_session["results"]["page_objects"].append(result)
        
        print(f"Page Object Model generated: {class_name}")
        return result

    def start_test_recording(self, test_name: str = None):
        """
        Start recording user interactions for test generation.

        Args:
            test_name (str, optional): Name for the test being recorded
        """
        if not self.browser_initialized:
            self.initialize_browser()
        
        self.test_generator.start_recording(test_name)
        
        if self.current_session:
            self.current_session["components_active"]["test_recording"] = True
        
        print("Test recording started")

    def stop_test_recording_and_generate(self, test_name: str = None) -> Dict[str, Any]:
        """
        Stop test recording and generate a test case.

        Args:
            test_name (str, optional): Name for the generated test

        Returns:
            Dict[str, Any]: Generated test case and recording data
        """
        if not self.test_generator:
            raise RuntimeError("Test generator not initialized")
        
        # Stop recording
        recorded_data = self.test_generator.stop_recording()
        
        if not recorded_data:
            raise RuntimeError("No recording data available")
        
        # Generate test case
        test_case = self.test_generator.generate_test_case_from_recording(recorded_data, test_name)
        
        # Save files
        recording_file = self.test_generator.save_recorded_data(recorded_data)
        test_file = self.test_generator.save_generated_test(test_case)
        
        result = {
            "test_case": test_case,
            "recorded_data": recorded_data,
            "recording_file": recording_file,
            "test_file": test_file,
            "generated_at": datetime.now().isoformat()
        }
        
        # Add to session results
        if self.current_session:
            self.current_session["components_active"]["test_recording"] = False
            self.current_session["results"]["recorded_tests"].append(result)
        
        print(f"Test case generated and saved: {test_file}")
        return result

    def start_performance_monitoring(self, test_name: str = None):
        """
        Start performance monitoring.

        Args:
            test_name (str, optional): Name for the performance test
        """
        if not self.browser_initialized:
            self.initialize_browser()
        
        self.performance_monitor.start_monitoring(test_name)
        
        if self.current_session:
            self.current_session["components_active"]["performance_monitoring"] = True
        
        print("Performance monitoring started")

    def stop_performance_monitoring(self) -> Dict[str, Any]:
        """
        Stop performance monitoring and generate report.

        Returns:
            Dict[str, Any]: Performance report
        """
        if not self.performance_monitor:
            raise RuntimeError("Performance monitor not initialized")
        
        # Stop monitoring
        performance_data = self.performance_monitor.stop_monitoring()
        
        if not performance_data:
            raise RuntimeError("No performance data available")
        
        # Save report
        report_file = self.performance_monitor.save_performance_report()
        
        result = {
            "performance_data": performance_data,
            "report_file": report_file,
            "summary": self.performance_monitor.get_performance_summary(),
            "generated_at": datetime.now().isoformat()
        }
        
        # Add to session results
        if self.current_session:
            self.current_session["components_active"]["performance_monitoring"] = False
            self.current_session["results"]["performance_reports"].append(result)
        
        print(f"Performance report generated: {report_file}")
        return result

    def measure_page_performance(self, url: str, wait_for_selector: str = None) -> Dict[str, Any]:
        """
        Measure performance for a specific page load.

        Args:
            url (str): URL to measure
            wait_for_selector (str, optional): Selector to wait for

        Returns:
            Dict[str, Any]: Performance metrics
        """
        if not self.browser_initialized:
            self.initialize_browser()
        
        print(f"Measuring page performance: {url}")
        
        performance_data = self.performance_monitor.measure_page_load(url, wait_for_selector)
        
        return {
            "url": url,
            "performance_data": performance_data,
            "measured_at": datetime.now().isoformat()
        }

    def find_element_with_ai(self, description: str, use_visual: bool = True) -> Dict[str, Any]:
        """
        Find element using AI-powered methods.

        Args:
            description (str): Element description
            use_visual (bool): Whether to use visual AI analysis

        Returns:
            Dict[str, Any]: Element finding results
        """
        if not self.browser_initialized:
            self.initialize_browser()
        
        print(f"Finding element with AI: {description}")
        
        try:
            if use_visual:
                locator, selector_info = self.visual_element_finder.find_element_with_visual_ai(description)
            else:
                locator, selector_info = self.visual_element_finder.find_element_with_auto_healing(description)
            
            # Cache the successful selector
            self.visual_element_finder.cache_element_selector(description, selector_info)
            
            return {
                "description": description,
                "found": True,
                "selector_info": selector_info,
                "element_count": locator.count() if locator else 0,
                "health_score": self.visual_element_finder.get_element_health_score(locator),
                "found_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "description": description,
                "found": False,
                "error": str(e),
                "attempted_at": datetime.now().isoformat()
            }

    def run_comprehensive_analysis(self, url: str, include_performance: bool = True, 
                                 include_pom: bool = True) -> Dict[str, Any]:
        """
        Run a comprehensive analysis of a web page.

        Args:
            url (str): URL to analyze
            include_performance (bool): Whether to include performance analysis
            include_pom (bool): Whether to generate Page Object Model

        Returns:
            Dict[str, Any]: Complete analysis results
        """
        if not self.browser_initialized:
            self.initialize_browser()
        
        print(f"Running comprehensive analysis for: {url}")
        
        analysis_results = {
            "url": url,
            "analysis_start": datetime.now().isoformat(),
            "components": {}
        }
        
        # Page Object Model generation
        if include_pom:
            try:
                pom_result = self.analyze_page_and_generate_pom(url)
                analysis_results["components"]["page_object_model"] = pom_result
                print("✓ Page Object Model generated")
            except Exception as e:
                analysis_results["components"]["page_object_model"] = {"error": str(e)}
                print(f"✗ POM generation failed: {e}")
        
        # Performance analysis
        if include_performance:
            try:
                perf_result = self.measure_page_performance(url)
                analysis_results["components"]["performance"] = perf_result
                print("✓ Performance analysis completed")
            except Exception as e:
                analysis_results["components"]["performance"] = {"error": str(e)}
                print(f"✗ Performance analysis failed: {e}")
        
        analysis_results["analysis_end"] = datetime.now().isoformat()
        
        # Save comprehensive report
        report_file = self._save_comprehensive_report(analysis_results)
        analysis_results["report_file"] = report_file
        
        # Add to session results
        if self.current_session:
            self.current_session["results"]["analysis_reports"].append(analysis_results)
        
        print(f"Comprehensive analysis completed: {report_file}")
        return analysis_results

    def _save_comprehensive_report(self, analysis_results: Dict[str, Any]) -> str:
        """Save comprehensive analysis report to file."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"comprehensive_analysis_{timestamp}.json"
        filepath = os.path.join("auto_playwright_reports", filename)
        
        # Ensure directory exists
        os.makedirs("auto_playwright_reports", exist_ok=True)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(analysis_results, f, indent=2, ensure_ascii=False)
        
        return filepath

    def end_session(self) -> Dict[str, Any]:
        """
        End the current auto session and generate summary.

        Returns:
            Dict[str, Any]: Session summary
        """
        if not self.current_session:
            return {"message": "No active session"}
        
        # Stop any active monitoring
        if self.current_session["components_active"]["test_recording"]:
            print("Warning: Test recording was still active, stopping...")
            try:
                self.stop_test_recording_and_generate()
            except Exception as e:
                print(f"Error stopping test recording: {e}")
        
        if self.current_session["components_active"]["performance_monitoring"]:
            print("Warning: Performance monitoring was still active, stopping...")
            try:
                self.stop_performance_monitoring()
            except Exception as e:
                print(f"Error stopping performance monitoring: {e}")
        
        # Finalize session
        self.current_session["end_time"] = datetime.now().isoformat()
        
        # Generate session summary
        summary = {
            "session_id": self.current_session["session_id"],
            "duration": self.current_session["end_time"],
            "results_summary": {
                "page_objects_generated": len(self.current_session["results"]["page_objects"]),
                "tests_recorded": len(self.current_session["results"]["recorded_tests"]),
                "performance_reports": len(self.current_session["results"]["performance_reports"]),
                "analysis_reports": len(self.current_session["results"]["analysis_reports"])
            },
            "session_data": self.current_session
        }
        
        # Save session summary
        session_file = self._save_session_summary(summary)
        summary["session_file"] = session_file
        
        print(f"Auto Playwright session ended: {self.current_session['session_id']}")
        print(f"Session summary saved: {session_file}")
        
        self.current_session = None
        return summary

    def _save_session_summary(self, summary: Dict[str, Any]) -> str:
        """Save session summary to file."""
        session_id = summary["session_id"]
        filename = f"session_summary_{session_id}.json"
        filepath = os.path.join("auto_playwright_reports", filename)
        
        # Ensure directory exists
        os.makedirs("auto_playwright_reports", exist_ok=True)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        
        return filepath

    def close(self):
        """Close browser and cleanup resources."""
        if self.current_session:
            self.end_session()
        
        if self.browser_manager:
            self.browser_manager.close()
        
        print("Auto Playwright Orchestrator closed")

    def get_status(self) -> Dict[str, Any]:
        """Get current status of the orchestrator."""
        return {
            "browser_initialized": self.browser_initialized,
            "current_session": self.current_session["session_id"] if self.current_session else None,
            "components_active": self.current_session["components_active"] if self.current_session else {},
            "llm_model": self.llm_service.model if self.llm_service else None
        }
