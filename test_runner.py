import os
from typing import Dict, Any, List
from automation_orchestrator import AutomationOrchestrator

class TestRunner:
    """
    Orchestrates the execution of a full test case.
    It uses the AutomationOrchestrator to perform the web actions
    and manages screenshot directories.
    """
    def __init__(self, api_key: str = None, model: str = "gpt-3.5-turbo"):
        """
        Initializes the TestRunner.

        Args:
            api_key (str, optional): OpenAI API key. Defaults to None.
            model (str, optional): The OpenAI model to use. Defaults to "gpt-3.5-turbo".
        """
        self.automation_orchestrator = AutomationOrchestrator(api_key=api_key, init_browser=True, model=model)

    def run_test_case(self, test_id: str, test_steps: List[Dict[str, Any]], run_count: int) -> Dict[str, Any]:
        """
        Runs a given test case, executing its steps and capturing results and screenshots.

        Args:
            test_id (str): The ID of the test case.
            test_steps (List[Dict[str, Any]]): The structured steps of the test case.
            run_count (int): The current run number for this test case.

        Returns:
            Dict[str, Any]: A dictionary containing the execution results and token usage.
        """
        screenshots_dir = f"data/{test_id}-run_{run_count}"
        os.makedirs(screenshots_dir, exist_ok=True)
        print(f"Screenshots will be saved to: {screenshots_dir}")

        try:
            execution_results = self.automation_orchestrator.execute_instructions(test_steps, screenshots_dir)
            tokens_used = self.automation_orchestrator.get_token_usage()

            # Debug: Log token usage in TestRunner
            print(f"[DEBUG TestRunner] Execution results count: {len(execution_results)}")
            print(f"[DEBUG TestRunner] Tokens used from orchestrator: {tokens_used}")
            print(f"[DEBUG TestRunner] Orchestrator LLM service token usage: {self.automation_orchestrator.llm_service.get_total_token_usage()}")

            return {
                "results": execution_results,
                "tokens_used": tokens_used,
                "screenshots_base_url": f"/data/{test_id}-run_{run_count}"
            }
        finally:
            self.automation_orchestrator.close()