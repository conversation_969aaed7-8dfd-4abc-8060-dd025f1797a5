import cv2
import numpy as np
from PIL import Image
import base64
import io
from typing import Dict, <PERSON>, Tu<PERSON>, Any, Optional
from playwright.sync_api import Page, Locator
from llm_service import LLMService
import json
import os


class AutoVisualElementFinder:
    """
    Enhanced element finder that uses visual recognition and AI to locate elements
    on web pages. Provides auto-healing selectors and smart element detection.
    """

    def __init__(self, page: Page, llm_service: LLMService):
        """
        Initialize the Auto Visual Element Finder.

        Args:
            page (Page): Playwright page instance
            llm_service (LLMService): LLM service for AI-powered element detection
        """
        self.page = page
        self.llm_service = llm_service
        self.element_cache = {}
        self.selector_history = {}
        self.visual_cache = {}

    def find_element_with_visual_ai(self, description: str, screenshot_path: str = None) -> Tuple[Locator, Dict[str, Any]]:
        """
        Find element using visual AI analysis of the page screenshot.

        Args:
            description (str): Natural language description of the element
            screenshot_path (str, optional): Path to save screenshot for analysis

        Returns:
            Tuple[Locator, Dict[str, Any]]: Element locator and selector info
        """
        # Take screenshot for analysis
        if not screenshot_path:
            screenshot_path = "temp_screenshot.png"
        
        screenshot_bytes = self.page.screenshot(path=screenshot_path, full_page=True)
        
        # Convert screenshot to base64 for LLM analysis
        screenshot_b64 = base64.b64encode(screenshot_bytes).decode()
        
        # Get page HTML for context
        page_html = self.page.content()
        
        # Use LLM to analyze screenshot and HTML
        prompt = f"""
        I need to find an element described as: "{description}" on a webpage.
        
        I'm providing you with:
        1. A screenshot of the current page (base64 encoded)
        2. The HTML content of the page
        
        Please analyze the screenshot and HTML to identify the best selector for this element.
        
        Screenshot (base64): {screenshot_b64[:1000]}...
        
        HTML (truncated): {page_html[:5000]}...
        
        Return a JSON object with:
        {{
            "strategy": "css|xpath|text|role|id",
            "value": "selector_value",
            "confidence": 0.95,
            "visual_description": "description of what you see",
            "coordinates": {{"x": 100, "y": 200}},
            "fallback_selectors": [
                {{"strategy": "css", "value": "alternative_selector"}},
                {{"strategy": "text", "value": "text_content"}}
            ]
        }}
        """
        
        try:
            response = self.llm_service.get_completion(prompt)
            selector_info = json.loads(response)
            
            # Try the primary selector
            primary_selector = selector_info.get('value', '')
            strategy = selector_info.get('strategy', 'css')
            
            locator = self._create_locator_from_strategy(strategy, primary_selector)
            
            if locator and locator.count() > 0:
                print(f"Found element using visual AI: {strategy}={primary_selector}")
                return locator.first, selector_info
            
            # Try fallback selectors
            fallbacks = selector_info.get('fallback_selectors', [])
            for fallback in fallbacks:
                try:
                    fb_locator = self._create_locator_from_strategy(
                        fallback.get('strategy', 'css'),
                        fallback.get('value', '')
                    )
                    if fb_locator and fb_locator.count() > 0:
                        print(f"Found element using fallback: {fallback}")
                        return fb_locator.first, fallback
                except Exception:
                    continue
                    
        except Exception as e:
            print(f"Error in visual AI element finding: {e}")
        
        # Clean up temporary screenshot
        if screenshot_path == "temp_screenshot.png" and os.path.exists(screenshot_path):
            os.remove(screenshot_path)
        
        raise Exception(f"Could not find element '{description}' using visual AI")

    def find_element_with_auto_healing(self, description: str, last_known_selector: Dict[str, Any] = None) -> Tuple[Locator, Dict[str, Any]]:
        """
        Find element with auto-healing capabilities. If the last known selector fails,
        automatically tries to find the element using alternative methods.

        Args:
            description (str): Element description
            last_known_selector (Dict[str, Any], optional): Previously working selector

        Returns:
            Tuple[Locator, Dict[str, Any]]: Element locator and selector info
        """
        # Try last known selector first
        if last_known_selector:
            try:
                strategy = last_known_selector.get('strategy', 'css')
                value = last_known_selector.get('value', '')
                
                locator = self._create_locator_from_strategy(strategy, value)
                if locator and locator.count() > 0:
                    print(f"Element found using cached selector: {strategy}={value}")
                    return locator.first, last_known_selector
                else:
                    print(f"Cached selector failed, attempting auto-healing for: {description}")
            except Exception as e:
                print(f"Cached selector error: {e}, attempting auto-healing")

        # Auto-healing: try multiple strategies
        healing_strategies = [
            self._try_semantic_locators,
            self._try_visual_similarity,
            self._try_text_matching,
            self._try_position_based,
            self._try_ai_powered_search
        ]

        for strategy_func in healing_strategies:
            try:
                result = strategy_func(description)
                if result:
                    locator, selector_info = result
                    print(f"Auto-healing successful using {strategy_func.__name__}")
                    
                    # Cache the new working selector
                    self.selector_history[description] = selector_info
                    return locator, selector_info
            except Exception as e:
                print(f"Auto-healing strategy {strategy_func.__name__} failed: {e}")
                continue

        raise Exception(f"Auto-healing failed for element: {description}")

    def _create_locator_from_strategy(self, strategy: str, value: str) -> Optional[Locator]:
        """Create a Playwright locator based on strategy and value."""
        try:
            if strategy == 'css':
                return self.page.locator(value)
            elif strategy == 'xpath':
                return self.page.locator(f"xpath={value}")
            elif strategy == 'text':
                return self.page.locator(value)
            elif strategy == 'id':
                return self.page.locator(f"#{value}")
            elif strategy == 'role':
                # Parse role selector (e.g., "button", {"name": "Submit"})
                if '{' in value:
                    role_name = value.split(',')[0].strip().strip('"\'')
                    return self.page.get_by_role(role_name)
                else:
                    return self.page.get_by_role(value)
            else:
                return self.page.locator(value)
        except Exception:
            return None

    def _try_semantic_locators(self, description: str) -> Optional[Tuple[Locator, Dict[str, Any]]]:
        """Try Playwright's built-in semantic locators."""
        # Extract potential role and text from description
        description_lower = description.lower()
        
        # Common element types and their roles
        role_mappings = {
            'button': 'button',
            'link': 'link',
            'input': 'textbox',
            'textbox': 'textbox',
            'checkbox': 'checkbox',
            'radio': 'radio',
            'dropdown': 'combobox',
            'select': 'combobox',
            'heading': 'heading',
            'image': 'img'
        }
        
        for keyword, role in role_mappings.items():
            if keyword in description_lower:
                try:
                    # Try by role
                    locator = self.page.get_by_role(role)
                    if locator.count() > 0:
                        return locator.first, {"strategy": "role", "value": role}
                    
                    # Try by role with name
                    words = description.split()
                    for word in words:
                        if len(word) > 3 and word.lower() not in ['button', 'link', 'input', 'the', 'and', 'with']:
                            try:
                                locator = self.page.get_by_role(role, name=word)
                                if locator.count() > 0:
                                    return locator.first, {"strategy": "role", "value": f"{role}, name={word}"}
                            except Exception:
                                continue
                except Exception:
                    continue
        
        return None

    def _try_visual_similarity(self, description: str) -> Optional[Tuple[Locator, Dict[str, Any]]]:
        """Try to find element based on visual similarity to cached elements."""
        # This is a placeholder for visual similarity matching
        # In a full implementation, this would use computer vision techniques
        # to match elements based on their visual appearance
        
        if description in self.visual_cache:
            cached_info = self.visual_cache[description]
            try:
                locator = self._create_locator_from_strategy(
                    cached_info['strategy'], 
                    cached_info['value']
                )
                if locator and locator.count() > 0:
                    return locator.first, cached_info
            except Exception:
                pass
        
        return None

    def _try_text_matching(self, description: str) -> Optional[Tuple[Locator, Dict[str, Any]]]:
        """Try to find element by matching text content."""
        # Extract potential text from description
        words = description.split()
        
        for i in range(len(words)):
            for j in range(i + 1, len(words) + 1):
                text_to_try = ' '.join(words[i:j])
                if len(text_to_try) > 2:
                    try:
                        # Try exact text match
                        locator = self.page.get_by_text(text_to_try, exact=True)
                        if locator.count() > 0:
                            return locator.first, {"strategy": "text", "value": f'text="{text_to_try}"'}
                        
                        # Try partial text match
                        locator = self.page.get_by_text(text_to_try)
                        if locator.count() > 0:
                            return locator.first, {"strategy": "text", "value": f'text=*{text_to_try}*'}
                    except Exception:
                        continue
        
        return None

    def _try_position_based(self, description: str) -> Optional[Tuple[Locator, Dict[str, Any]]]:
        """Try to find element based on position keywords in description."""
        position_keywords = {
            'first': 'first',
            'last': 'last',
            'top': 'first',
            'bottom': 'last',
            'second': 'nth(1)',
            'third': 'nth(2)'
        }
        
        description_lower = description.lower()
        
        for keyword, selector_modifier in position_keywords.items():
            if keyword in description_lower:
                # Try to find base elements and apply position
                base_selectors = ['button', 'input', 'a', 'div', 'span']
                
                for base_selector in base_selectors:
                    try:
                        if selector_modifier.startswith('nth'):
                            locator = self.page.locator(base_selector).nth(int(selector_modifier.split('(')[1].split(')')[0]))
                        elif selector_modifier == 'first':
                            locator = self.page.locator(base_selector).first
                        elif selector_modifier == 'last':
                            locator = self.page.locator(base_selector).last
                        else:
                            continue
                            
                        if locator.count() > 0:
                            return locator, {"strategy": "css", "value": f"{base_selector}:{selector_modifier}"}
                    except Exception:
                        continue
        
        return None

    def _try_ai_powered_search(self, description: str) -> Optional[Tuple[Locator, Dict[str, Any]]]:
        """Use AI to generate smart selectors based on page content."""
        try:
            page_html = self.page.content()
            
            prompt = f"""
            Find the best selector for an element described as: "{description}"
            
            Page HTML (truncated): {page_html[:8000]}
            
            Analyze the HTML and provide the most reliable selector.
            Return only a JSON object:
            {{
                "strategy": "css|xpath|text|role",
                "value": "selector_value"
            }}
            """
            
            response = self.llm_service.get_completion(prompt)
            selector_info = json.loads(response)
            
            strategy = selector_info.get('strategy', 'css')
            value = selector_info.get('value', '')
            
            locator = self._create_locator_from_strategy(strategy, value)
            if locator and locator.count() > 0:
                return locator.first, selector_info
                
        except Exception as e:
            print(f"AI-powered search failed: {e}")
        
        return None

    def cache_element_selector(self, description: str, selector_info: Dict[str, Any]):
        """Cache a working selector for future use."""
        self.selector_history[description] = selector_info
        print(f"Cached selector for '{description}': {selector_info}")

    def get_cached_selector(self, description: str) -> Optional[Dict[str, Any]]:
        """Get cached selector for an element description."""
        return self.selector_history.get(description)

    def clear_cache(self):
        """Clear all cached selectors and visual data."""
        self.element_cache.clear()
        self.selector_history.clear()
        self.visual_cache.clear()
        print("Element finder cache cleared")

    def get_element_health_score(self, locator: Locator) -> float:
        """
        Calculate a health score for an element selector (0.0 to 1.0).
        Higher scores indicate more reliable selectors.
        """
        try:
            if not locator or locator.count() == 0:
                return 0.0
            
            score = 0.0
            
            # Check if element is unique (higher score for unique elements)
            if locator.count() == 1:
                score += 0.4
            elif locator.count() <= 3:
                score += 0.2
            
            # Check if element is visible
            if locator.first.is_visible():
                score += 0.3
            
            # Check if element is enabled (for interactive elements)
            try:
                if locator.first.is_enabled():
                    score += 0.2
            except Exception:
                score += 0.1  # Non-interactive elements get partial score
            
            # Check stability (element still exists after small delay)
            self.page.wait_for_timeout(100)
            if locator.count() > 0:
                score += 0.1
            
            return min(score, 1.0)
            
        except Exception:
            return 0.0
