#!/usr/bin/env python3
"""
Test script for Auto Playwright functionality.
Demonstrates natural language web automation using the auto() function.
"""

import os
from dotenv import load_dotenv
from browser_manager import <PERSON>rowserManager
from llm_service import LLMService
from auto_playwright import AutoPlaywright

# Load environment variables
load_dotenv()

def test_auto_playwright_basic():
    """Test basic auto-playwright functionality."""
    print("🚀 Testing Auto Playwright Basic Functionality")
    print("=" * 50)
    
    # Initialize browser
    browser_manager = BrowserManager(headless=False)  # Set to True for headless mode
    browser_manager.launch_browser()
    page = browser_manager.get_page()
    
    # Initialize LLM service
    llm_service = LLMService(api_key=os.getenv("OPENAI_API_KEY"))
    
    # Initialize auto-playwright
    auto_pw = AutoPlaywright(page, llm_service, debug=True)
    
    try:
        # Test 1: Navigate to a website
        print("\n📍 Test 1: Navigation")
        result = auto_pw.auto("Go to https://example.com")
        print(f"Navigation result: {result}")
        
        # Test 2: Find and interact with elements
        print("\n🔍 Test 2: Element Interaction")
        result = auto_pw.auto("Find the 'More information...' link")
        print(f"Element finding result: {result}")
        
        # Test 3: Extract information
        print("\n📄 Test 3: Information Extraction")
        result = auto_pw.auto("Get the main heading text from the page")
        print(f"Text extraction result: {result}")
        
        # Test 4: Verify page content
        print("\n✅ Test 4: Content Verification")
        result = auto_pw.auto("Check if the page contains the text 'Example Domain'")
        print(f"Verification result: {result}")
        
        print("\n🎉 All tests completed!")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
    
    finally:
        # Cleanup
        browser_manager.close()
        print("\n🧹 Browser closed")

def test_auto_playwright_form_interaction():
    """Test auto-playwright with form interactions."""
    print("\n🚀 Testing Auto Playwright Form Interaction")
    print("=" * 50)
    
    # Initialize browser
    browser_manager = BrowserManager(headless=False)
    browser_manager.launch_browser()
    page = browser_manager.get_page()
    
    # Initialize LLM service
    llm_service = LLMService(api_key=os.getenv("OPENAI_API_KEY"))
    
    # Initialize auto-playwright
    auto_pw = AutoPlaywright(page, llm_service, debug=True)
    
    try:
        # Navigate to a form demo page
        print("\n📍 Navigating to form demo")
        result = auto_pw.auto("Go to https://httpbin.org/forms/post")
        print(f"Navigation result: {result}")
        
        # Fill out the form
        print("\n📝 Filling out the form")
        result = auto_pw.auto("Fill the customer name field with 'John Doe'")
        print(f"Name fill result: {result}")
        
        result = auto_pw.auto("Fill the telephone field with '555-1234'")
        print(f"Phone fill result: {result}")
        
        result = auto_pw.auto("Fill the email field with '<EMAIL>'")
        print(f"Email fill result: {result}")
        
        # Select from dropdown
        result = auto_pw.auto("Select 'Medium' from the pizza size dropdown")
        print(f"Dropdown selection result: {result}")
        
        # Check verification
        print("\n✅ Verifying form content")
        result = auto_pw.auto("Check if the customer name field contains 'John Doe'")
        print(f"Verification result: {result}")
        
        print("\n🎉 Form interaction tests completed!")
        
    except Exception as e:
        print(f"❌ Error during form testing: {e}")
    
    finally:
        # Cleanup
        browser_manager.close()
        print("\n🧹 Browser closed")

def test_auto_playwright_search():
    """Test auto-playwright with search functionality."""
    print("\n🚀 Testing Auto Playwright Search")
    print("=" * 50)
    
    # Initialize browser
    browser_manager = BrowserManager(headless=False)
    browser_manager.launch_browser()
    page = browser_manager.get_page()
    
    # Initialize LLM service
    llm_service = LLMService(api_key=os.getenv("OPENAI_API_KEY"))
    
    # Initialize auto-playwright
    auto_pw = AutoPlaywright(page, llm_service, debug=True)
    
    try:
        # Navigate to DuckDuckGo
        print("\n📍 Navigating to DuckDuckGo")
        result = auto_pw.auto("Go to https://duckduckgo.com")
        print(f"Navigation result: {result}")
        
        # Perform search
        print("\n🔍 Performing search")
        result = auto_pw.auto("Search for 'playwright automation'")
        print(f"Search result: {result}")
        
        # Wait for results and extract information
        print("\n📄 Extracting search results")
        result = auto_pw.auto("Get the title of the first search result")
        print(f"First result title: {result}")
        
        # Verify search results
        print("\n✅ Verifying search results")
        result = auto_pw.auto("Check if the page contains search results about playwright")
        print(f"Verification result: {result}")
        
        print("\n🎉 Search tests completed!")
        
    except Exception as e:
        print(f"❌ Error during search testing: {e}")
    
    finally:
        # Cleanup
        browser_manager.close()
        print("\n🧹 Browser closed")

def main():
    """Run all auto-playwright tests."""
    print("🤖 Auto Playwright Test Suite")
    print("=" * 60)
    
    # Check if OpenAI API key is available
    if not os.getenv("OPENAI_API_KEY"):
        print("❌ Error: OPENAI_API_KEY environment variable not set")
        print("Please set your OpenAI API key in the .env file")
        return
    
    try:
        # Run basic tests
        test_auto_playwright_basic()
        
        # Ask user if they want to continue with more tests
        user_input = input("\n🤔 Continue with form interaction tests? (y/n): ")
        if user_input.lower() in ['y', 'yes']:
            test_auto_playwright_form_interaction()
        
        user_input = input("\n🤔 Continue with search tests? (y/n): ")
        if user_input.lower() in ['y', 'yes']:
            test_auto_playwright_search()
        
        print("\n🎊 All Auto Playwright tests completed successfully!")
        
    except KeyboardInterrupt:
        print("\n⏹️ Tests interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")

if __name__ == "__main__":
    main()
