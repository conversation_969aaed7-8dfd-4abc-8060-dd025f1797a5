import uvicorn
import os
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
from datetime import datetime

# Import .env file
from dotenv import load_dotenv

# Import CORS middleware
from fastapi.middleware.cors import CORSMiddleware

# Import for serving static files
from fastapi.staticfiles import StaticFiles

# Import modularized components
from models import Step, TestCase, TestResult # Import models from models.py
from test_case_manager import Test<PERSON>aseManager
from automation_orchestrator import AutomationOrchestrator
from test_runner import TestRunner
from auto_playwright import AutoPlaywright
from llm_service import LLMService
from browser_manager import BrowserManager

load_dotenv()
app = FastAPI()

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # For development, you can use ["*"]. For production, specify your frontend domain
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create data directory if it doesn't exist
os.makedirs("data", exist_ok=True)

# Mount the data directory to serve static files
app.mount("/data", StaticFiles(directory="data"), name="data")

# Initialize managers
test_case_manager = TestCaseManager(file_path="test_cases.json")
# AutomationOrchestrator and TestRunner will be initialized on demand for parsing/running
# or can be initialized once if `init_browser=False` is acceptable for parsing.

@app.post("/create-test-case")
def create_test_case(test_case: TestCase):
    """
    Create a new test case by parsing natural language instructions.
    """
    try:
        # Initialize AutomationOrchestrator for parsing without launching browser
        # The browser is not needed for just parsing instructions.
        automation_parser_only = AutomationOrchestrator(
            api_key=os.getenv("OPENAI_API_KEY"),
            init_browser=False # Do not launch browser for parsing
        )
        parsed_steps = automation_parser_only.parse_natural_language_instructions(test_case.natural_language_input)
        tokens_used_for_parsing = automation_parser_only.get_token_usage()

        new_test = test_case_manager.add_test_case(
            name=test_case.name,
            description=test_case.description,
            natural_language_input=test_case.natural_language_input,
            steps=parsed_steps
        )

        return {
            "message": "Test case created successfully.",
            "test_id": new_test["id"],
            "steps": new_test["steps"],
            "tokens_used_for_parsing": tokens_used_for_parsing
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create test case: {e}")

@app.get("/test-list")
def list_test_cases():
    """
    List all test cases.
    """
    all_tests = test_case_manager.get_all_test_cases()
    # Convert dictionary to list of test case details for response
    return [
        {
            "id": test_id,
            "name": details["name"],
            "description": details.get("description", ""),
            "created_at": details.get("created_at"),
            "last_run": details.get("last_run"),
            "run_count": details.get("run_count", 0),
        }
        for test_id, details in all_tests.items()
    ]

@app.get("/test-case/{test_id}")
def get_test_case(test_id: str):
    """
    Retrieve a specific test case by its ID.
    """
    try:
        test_case = test_case_manager.get_test_case(test_id)
        return test_case
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))

@app.put("/update-test-case/{test_id}")
def update_test_case(test_id: str, test_case_update: TestCase):
    """
    Update an existing test case.
    Note: This will re-parse the natural_language_input if provided.
    """
    try:
        current_test_case = test_case_manager.get_test_case(test_id)

        updates = test_case_update.dict(exclude_unset=True)

        # If natural_language_input is updated, re-parse the steps
        if "natural_language_input" in updates:
            automation_parser_only = AutomationOrchestrator(
                api_key=os.getenv("OPENAI_API_KEY"),
                init_browser=False
            )
            parsed_steps = automation_parser_only.parse_natural_language_instructions(updates["natural_language_input"])
            updates["steps"] = parsed_steps
            updates["tokens_used_for_parsing"] = automation_parser_only.get_token_usage()

        test_case_manager.update_test_case(test_id, updates)
        return {"message": f"Test case '{test_case_update.name}' updated successfully."}
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update test case: {e}")


@app.delete("/delete-test-case/{test_id}")
def delete_test_case(test_id: str):
    """
    Delete a test case by its ID.
    """
    try:
        test_case_manager.delete_test_case(test_id)
        return {"message": f"Test case '{test_id}' deleted successfully."}
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))

@app.post("/run-test-case/{test_id}")
def run_test_case(test_id: str):
    """
    Run a test case and return step-by-step results.
    """
    try:
        test = test_case_manager.get_test_case(test_id)

        # Increment run count and update last run time
        test_case_manager.increment_run_count(test_id)
        # Get the updated test case to retrieve the new run_count
        updated_test = test_case_manager.get_test_case(test_id)
        run_count = updated_test["run_count"]

        # Initialize TestRunner for execution (this will launch browser)
        test_runner = TestRunner(api_key=os.getenv("OPENAI_API_KEY"))
        run_results = test_runner.run_test_case(test_id, test["steps"], run_count)

        # After execution, test["steps"] will have updated detected_selector values.
        # Save these changes back to the test case file.
        try:
            test_case_manager.update_test_case(test_id, {"steps": test["steps"]})
            print(f"Successfully updated detected selectors for test case: {test_id}")
        except Exception as e_update:
            # Log this error but don't let it overshadow the test run result
            print(f"Error updating detected selectors for test case {test_id} after run: {e_update}")

        return {
            "message": f"Test case '{test['name']}' executed successfully.",
            "results": run_results["results"],
            "tokens_used": run_results["tokens_used"],
            "screenshots_base_url": run_results["screenshots_base_url"]
        }
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error running test case: {e}")


# Auto Playwright endpoints
@app.post("/auto-playwright/execute")
def execute_auto_playwright_task(request: dict):
    """
    Execute a natural language task using auto-playwright.

    Request body:
    {
        "task": "Click the login button",
        "url": "https://example.com" (optional),
        "debug": false (optional)
    }
    """
    try:
        task = request.get("task")
        url = request.get("url")
        debug = request.get("debug", False)

        if not task:
            raise HTTPException(status_code=400, detail="Task is required")

        # Initialize browser and auto-playwright
        browser_manager = BrowserManager(headless=True)
        browser_manager.launch_browser()
        page = browser_manager.get_page()

        # Navigate to URL if provided
        if url:
            page.goto(url)

        # Initialize LLM service and auto-playwright
        llm_service = LLMService(api_key=os.getenv("OPENAI_API_KEY"))
        auto_pw = AutoPlaywright(page, llm_service, debug=debug)

        # Execute the task
        result = auto_pw.auto(task)

        # Get current page info
        current_url = page.url
        page_title = page.title()

        # Cleanup
        browser_manager.close()

        return {
            "task": task,
            "result": result,
            "page_info": {
                "url": current_url,
                "title": page_title
            },
            "success": True
        }

    except Exception as e:
        # Ensure cleanup on error
        try:
            browser_manager.close()
        except:
            pass
        raise HTTPException(status_code=500, detail=f"Failed to execute auto-playwright task: {e}")


@app.post("/auto-playwright/create-test-from-natural-language")
def create_test_from_natural_language(request: dict):
    """
    Create a test case using natural language with auto-playwright.

    Request body:
    {
        "name": "Login Test",
        "description": "Test user login functionality",
        "tasks": [
            "Go to https://example.com",
            "Click the login button",
            "Fill in email with '<EMAIL>'",
            "Fill in password with 'password123'",
            "Click submit",
            "Verify that 'Welcome' text is visible"
        ]
    }
    """
    try:
        name = request.get("name")
        description = request.get("description", "")
        tasks = request.get("tasks", [])

        if not name or not tasks:
            raise HTTPException(status_code=400, detail="Name and tasks are required")

        # Convert natural language tasks to structured steps
        structured_steps = []

        for task in tasks:
            # Simple parsing - in a real implementation, you'd use the LLM to parse these
            task_lower = task.lower().strip()

            if task_lower.startswith("go to ") or task_lower.startswith("navigate to "):
                url = task.split(" ", 2)[-1]
                structured_steps.append({
                    "action": "goto",
                    "url": url,
                    "natural_language": task
                })
            elif "click" in task_lower:
                element_desc = task.replace("click", "").replace("Click", "").strip()
                structured_steps.append({
                    "action": "auto_click",
                    "element_description": element_desc,
                    "natural_language": task
                })
            elif "fill" in task_lower and "with" in task_lower:
                parts = task.split(" with ")
                field_desc = parts[0].replace("fill", "").replace("Fill", "").strip()
                value = parts[1].strip().strip("'\"")
                structured_steps.append({
                    "action": "auto_fill",
                    "field_description": field_desc,
                    "value": value,
                    "natural_language": task
                })
            elif "verify" in task_lower or "check" in task_lower:
                assertion_desc = task.replace("verify", "").replace("Verify", "").replace("check", "").replace("Check", "").strip()
                structured_steps.append({
                    "action": "auto_assert",
                    "assertion_description": assertion_desc,
                    "natural_language": task
                })
            else:
                # Generic auto task
                structured_steps.append({
                    "action": "auto_task",
                    "task_description": task,
                    "natural_language": task
                })

        # Create the test case
        new_test = test_case_manager.add_test_case(
            name=name,
            description=description,
            natural_language_input="\n".join(tasks),
            steps=structured_steps
        )

        return {
            "message": "Auto-playwright test case created successfully.",
            "test_id": new_test["id"],
            "steps": new_test["steps"],
            "total_tasks": len(tasks)
        }

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create auto-playwright test case: {e}")


@app.post("/auto-playwright/demo")
def run_auto_playwright_demo():
    """
    Run a demo test case to showcase auto-playwright functionality.
    This uses the existing WindQ test execution system.
    """
    try:
        # Load the demo test case
        import json
        with open("auto_playwright_demo.json", "r") as f:
            demo_test = json.load(f)

        # Add the test case to the system
        new_test = test_case_manager.add_test_case(
            name=demo_test["name"],
            description=demo_test["description"],
            steps=demo_test["steps"]
        )

        # Run the test case
        test_runner = TestRunner(api_key=os.getenv("OPENAI_API_KEY"))
        run_result = test_runner.run_test_case(
            test_id=new_test["id"],
            test_steps=new_test["steps"],
            run_count=1
        )

        return {
            "message": "Auto-playwright demo completed successfully",
            "test_id": new_test["id"],
            "test_name": demo_test["name"],
            "execution_results": run_result["results"],
            "tokens_used": run_result["tokens_used"],
            "screenshots_url": run_result["screenshots_base_url"]
        }

    except FileNotFoundError:
        raise HTTPException(status_code=404, detail="Demo test case file not found")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to run auto-playwright demo: {e}")


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)

