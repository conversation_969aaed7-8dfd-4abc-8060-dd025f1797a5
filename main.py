import uvicorn
import os
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
from datetime import datetime

# Import .env file
from dotenv import load_dotenv

# Import CORS middleware
from fastapi.middleware.cors import CORSMiddleware

# Import for serving static files
from fastapi.staticfiles import StaticFiles

# Import modularized components
from models import Step, TestCase, TestResult # Import models from models.py
from test_case_manager import Test<PERSON>aseManager
from automation_orchestrator import AutomationOrchestrator
from test_runner import TestRunner

load_dotenv()
app = FastAPI()

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # For development, you can use ["*"]. For production, specify your frontend domain
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create data directory if it doesn't exist
os.makedirs("data", exist_ok=True)

# Mount the data directory to serve static files
app.mount("/data", StaticFiles(directory="data"), name="data")

# Initialize managers
test_case_manager = TestCaseManager(file_path="test_cases.json")
# AutomationOrchestrator and TestRunner will be initialized on demand for parsing/running
# or can be initialized once if `init_browser=False` is acceptable for parsing.

@app.post("/create-test-case")
def create_test_case(test_case: TestCase):
    """
    Create a new test case by parsing natural language instructions.
    """
    try:
        # Initialize AutomationOrchestrator for parsing without launching browser
        # The browser is not needed for just parsing instructions.
        # API key will be auto-detected from environment (Azure OpenAI or standard OpenAI)
        automation_parser_only = AutomationOrchestrator(
            api_key=None,  # Will auto-detect Azure OpenAI or standard OpenAI
            init_browser=False # Do not launch browser for parsing
        )
        parsed_steps = automation_parser_only.parse_natural_language_instructions(test_case.natural_language_input)
        tokens_used_for_parsing = automation_parser_only.get_token_usage()

        new_test = test_case_manager.add_test_case(
            name=test_case.name,
            description=test_case.description,
            natural_language_input=test_case.natural_language_input,
            steps=parsed_steps
        )

        return {
            "message": "Test case created successfully.",
            "test_id": new_test["id"],
            "steps": new_test["steps"],
            "tokens_used_for_parsing": tokens_used_for_parsing
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create test case: {e}")

@app.get("/test-list")
def list_test_cases():
    """
    List all test cases.
    """
    all_tests = test_case_manager.get_all_test_cases()
    # Convert dictionary to list of test case details for response
    return [
        {
            "id": test_id,
            "name": details["name"],
            "description": details.get("description", ""),
            "created_at": details.get("created_at"),
            "last_run": details.get("last_run"),
            "run_count": details.get("run_count", 0),
        }
        for test_id, details in all_tests.items()
    ]

@app.get("/test-case/{test_id}")
def get_test_case(test_id: str):
    """
    Retrieve a specific test case by its ID.
    """
    try:
        test_case = test_case_manager.get_test_case(test_id)
        return test_case
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))

@app.put("/update-test-case/{test_id}")
def update_test_case(test_id: str, test_case_update: TestCase):
    """
    Update an existing test case.
    Note: This will re-parse the natural_language_input if provided.
    """
    try:
        current_test_case = test_case_manager.get_test_case(test_id)

        updates = test_case_update.dict(exclude_unset=True)

        # If natural_language_input is updated, re-parse the steps
        if "natural_language_input" in updates:
            automation_parser_only = AutomationOrchestrator(
                api_key=None,  # Will auto-detect Azure OpenAI or standard OpenAI
                init_browser=False
            )
            parsed_steps = automation_parser_only.parse_natural_language_instructions(updates["natural_language_input"])
            updates["steps"] = parsed_steps
            updates["tokens_used_for_parsing"] = automation_parser_only.get_token_usage()

        test_case_manager.update_test_case(test_id, updates)
        return {"message": f"Test case '{test_case_update.name}' updated successfully."}
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update test case: {e}")


@app.delete("/delete-test-case/{test_id}")
def delete_test_case(test_id: str):
    """
    Delete a test case by its ID.
    """
    try:
        test_case_manager.delete_test_case(test_id)
        return {"message": f"Test case '{test_id}' deleted successfully."}
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))



@app.post("/run-test-case/{test_id}")
def run_test_case(test_id: str):
    """
    Run a test case and return step-by-step results.
    """
    try:
        test = test_case_manager.get_test_case(test_id)

        # Increment run count and update last run time
        test_case_manager.increment_run_count(test_id)
        # Get the updated test case to retrieve the new run_count
        updated_test = test_case_manager.get_test_case(test_id)
        run_count = updated_test["run_count"]

        # Initialize TestRunner for execution (this will launch browser)
        # API key will be auto-detected from environment (Azure OpenAI or standard OpenAI)
        test_runner = TestRunner(api_key=None)
        run_results = test_runner.run_test_case(test_id, test["steps"], run_count)

        # After execution, test["steps"] will have updated detected_selector values.
        # Save these changes back to the test case file.
        try:
            test_case_manager.update_test_case(test_id, {"steps": test["steps"]})
            print(f"Successfully updated detected selectors for test case: {test_id}")
        except Exception as e_update:
            # Log this error but don't let it overshadow the test run result
            print(f"Error updating detected selectors for test case {test_id} after run: {e_update}")

        return {
            "message": f"Test case '{test['name']}' executed successfully.",
            "results": run_results["results"],
            "tokens_used": run_results["tokens_used"],
            "screenshots_base_url": run_results["screenshots_base_url"]
        }
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error running test case: {e}")

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)

