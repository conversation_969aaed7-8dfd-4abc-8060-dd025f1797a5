import os
from typing import List, Optional, Dict
from jira import JIRA
from models import JiraStory, JiraEpic, JiraStoriesResponse, JiraAttachment
import logging

# Configure logging
logging.basicConfig(level=logging.WARNING)
logger = logging.getLogger(__name__)

class JiraService:
    """
    Service class for interacting with Jira API to fetch story and epic details.
    """
    
    def __init__(self):
        """
        Initialize Jira service with credentials from environment variables.
        """
        self.server_url = os.getenv("JIRA_SERVER_URL")
        self.username = os.getenv("JIRA_USERNAME")
        self.api_token = os.getenv("JIRA_API_TOKEN")
        
        if not all([self.server_url, self.username, self.api_token]):
            raise ValueError("Missing Jira credentials. Please set JIRA_SERVER_URL, JIRA_USERNAME, and JIRA_API_TOKEN environment variables.")
        
        # Remove quotes from API token if present
        self.api_token = self.api_token.strip('"')
        
        try:
            self.jira = JIRA(
                server=self.server_url,
                basic_auth=(self.username, self.api_token)
            )
            logger.info("Successfully connected to <PERSON><PERSON>")
        except Exception as e:
            logger.error(f"Failed to connect to Jira: {e}")
            raise
    
    def get_stories_by_project(self, project_key: str, max_results: int = 50) -> JiraStoriesResponse:
        """
        Fetch all stories from a specific project with their epic details.
        
        Args:
            project_key (str): The Jira project key (e.g., 'PROJ')
            max_results (int): Maximum number of results to return
            
        Returns:
            JiraStoriesResponse: Response containing list of stories with epic details
        """
        try:
            # JQL to fetch stories from the project
            jql = f'project = "{project_key}" AND issuetype = Story ORDER BY created DESC'
            
            # Fetch issues with expanded fields - include attachments
            issues = self.jira.search_issues(
                jql,
                maxResults=max_results,
                expand='changelog',
                fields='*all,attachment'  # Fetch all fields including attachments
            )
            
            stories = []
            for issue in issues:
                story = self._convert_issue_to_story(issue)
                stories.append(story)
            
            return JiraStoriesResponse(
                project=project_key,
                stories=stories,
                total_count=len(stories)
            )
            
        except Exception as e:
            logger.error(f"Error fetching stories for project {project_key}: {e}")
            raise
    
    def _convert_issue_to_story(self, issue) -> JiraStory:
        """
        Convert a Jira issue to a JiraStory model.
        
        Args:
            issue: Jira issue object
            
        Returns:
            JiraStory: Converted story object
        """
        try:
            # Extract basic story information
            story_id = issue.key
            title = issue.fields.summary
            project_name = issue.fields.project.name
            
            # Extract description and acceptance criteria
            description = getattr(issue.fields, 'description', None)
            story_description = description if description else None
            
            # Try to extract acceptance criteria from description or custom fields
            story_acceptance_criteria = self._extract_acceptance_criteria(issue)
            
            # Extract priority
            priority = None
            if hasattr(issue.fields, 'priority') and issue.fields.priority:
                priority = issue.fields.priority.name

            # Extract status
            status = None
            if hasattr(issue.fields, 'status') and issue.fields.status:
                status = issue.fields.status.name

            # Extract epic information
            epic = self._get_epic_details(issue)

            # Extract attachments
            attachments = self._get_attachments(issue)

            return JiraStory(
                id=story_id,
                title=title,
                project_name=project_name,
                story_description=story_description,
                story_acceptance_criteria=story_acceptance_criteria,
                priority=priority,
                status=status,
                epic=epic,
                attachments=attachments,
                attachment_count=len(attachments)
            )
            
        except Exception as e:
            logger.error(f"Error converting issue {issue.key} to story: {e}")
            raise
    
    def _extract_acceptance_criteria(self, issue) -> Optional[str]:
        """
        Extract acceptance criteria from issue description or custom fields.

        Args:
            issue: Jira issue object

        Returns:
            Optional[str]: Acceptance criteria if found
        """
        try:
            # First, try to find acceptance criteria in description
            description = getattr(issue.fields, 'description', None)
            if description:
                description_str = str(description)

                # Look for common acceptance criteria patterns (case insensitive)
                patterns = [
                    'acceptance criteria',
                    'acceptance criterion',
                    'ac:',
                    'acceptance:',
                    'criteria:',
                    'given when then',
                    'scenario:',
                    'scenarios:'
                ]

                description_lower = description_str.lower()
                for pattern in patterns:
                    if pattern in description_lower:
                        # Find the position and extract text after the pattern
                        pattern_pos = description_lower.find(pattern)
                        if pattern_pos != -1:
                            # Extract everything after the pattern
                            ac_text = description_str[pattern_pos + len(pattern):].strip()

                            # Clean up the text - remove leading colons and asterisks
                            ac_text = ac_text.lstrip(':*').strip()

                            # Stop at common section separators
                            separators = ['\n\n---', '\n\nDescription:', '\n\nNotes:', '\n\nComments:', '\n\nDefinition of Done']
                            for sep in separators:
                                if sep in ac_text:
                                    ac_text = ac_text.split(sep)[0].strip()

                            # Clean up markdown formatting
                            ac_text = ac_text.replace('*', '').replace('#', '').strip()

                            # Remove extra whitespace and normalize line breaks
                            lines = [line.strip() for line in ac_text.split('\n') if line.strip()]
                            ac_text = '\n'.join(lines)

                            if ac_text and len(ac_text) > 10:  # Ensure we have meaningful content
                                return ac_text

            # Try to get from custom fields - check all custom fields that might contain AC
            all_fields = dir(issue.fields)
            custom_fields = [field for field in all_fields if field.startswith('customfield_')]

            # Common acceptance criteria field names/patterns
            ac_field_patterns = [
                'acceptance',
                'criteria',
                'ac',
                'scenario',
                'given',
                'when',
                'then'
            ]

            for field_name in custom_fields:
                try:
                    field_value = getattr(issue.fields, field_name, None)
                    if field_value:
                        # Handle different field value types
                        extracted_text = self._extract_text_from_field(field_value)
                        if extracted_text:
                            field_str = extracted_text.lower()
                            # Check if this field might contain acceptance criteria
                            if any(pattern in field_str for pattern in ac_field_patterns):
                                return extracted_text

                            # Also check if the field value looks like acceptance criteria
                            if ('given' in field_str and 'when' in field_str) or \
                               ('scenario' in field_str) or \
                               (len(field_str) > 50 and 'should' in field_str):
                                return extracted_text

                except Exception:
                    continue

            return None

        except Exception as e:
            logger.warning(f"Error extracting acceptance criteria for issue {getattr(issue, 'key', 'unknown')}: {e}")
            return None

    def _extract_text_from_field(self, field_value) -> Optional[str]:
        """
        Extract text from various Jira field types.

        Args:
            field_value: The field value from Jira (could be string, list, object, etc.)

        Returns:
            Optional[str]: Extracted text or None
        """
        try:
            # Handle None values
            if field_value is None:
                return None

            # Handle string values
            if isinstance(field_value, str):
                return field_value.strip() if field_value.strip() else None

            # Handle list values (like PropertyHolder objects in lists)
            if isinstance(field_value, list):
                text_parts = []
                for item in field_value:
                    item_text = self._extract_text_from_field(item)
                    if item_text:
                        text_parts.append(item_text)
                return '\n'.join(text_parts) if text_parts else None

            # Handle PropertyHolder and similar objects
            if hasattr(field_value, 'value'):
                return self._extract_text_from_field(field_value.value)

            # Handle objects with 'content' attribute
            if hasattr(field_value, 'content'):
                return self._extract_text_from_field(field_value.content)

            # Handle objects with 'text' attribute
            if hasattr(field_value, 'text'):
                return self._extract_text_from_field(field_value.text)

            # Handle dict-like objects
            if hasattr(field_value, '__dict__'):
                # Try common text fields in the object
                for attr in ['value', 'content', 'text', 'body', 'description']:
                    if hasattr(field_value, attr):
                        attr_value = getattr(field_value, attr)
                        if attr_value:
                            extracted = self._extract_text_from_field(attr_value)
                            if extracted:
                                return extracted

            # Handle Atlassian Document Format (ADF) - common in newer Jira versions
            if isinstance(field_value, dict):
                if 'content' in field_value:
                    return self._extract_text_from_adf(field_value)
                elif 'value' in field_value:
                    return self._extract_text_from_field(field_value['value'])

            # Fallback to string conversion
            text = str(field_value).strip()
            # Avoid returning object representations
            if not text.startswith('<') or not text.endswith('>'):
                return text if text else None

            return None

        except Exception:
            return None

    def _extract_text_from_adf(self, adf_content) -> Optional[str]:
        """
        Extract text from Atlassian Document Format (ADF).

        Args:
            adf_content: ADF content dictionary

        Returns:
            Optional[str]: Extracted text
        """
        try:
            if not isinstance(adf_content, dict):
                return None

            text_parts = []

            def extract_from_node(node):
                if isinstance(node, dict):
                    # Handle text nodes
                    if node.get('type') == 'text' and 'text' in node:
                        text_parts.append(node['text'])

                    # Handle content arrays
                    if 'content' in node and isinstance(node['content'], list):
                        for child in node['content']:
                            extract_from_node(child)

                elif isinstance(node, list):
                    for item in node:
                        extract_from_node(item)

            extract_from_node(adf_content)

            return '\n'.join(text_parts).strip() if text_parts else None

        except Exception:
            return None
    
    def _get_epic_details(self, issue) -> Optional[JiraEpic]:
        """
        Get epic details for a story.

        Args:
            issue: Jira issue object

        Returns:
            Optional[JiraEpic]: Epic details if the story belongs to an epic
        """
        try:
            # Find epic link field dynamically - common field names for epic links
            epic_link = None
            epic_link_fields = [
                'customfield_10014',  # Common epic link field
                'customfield_10008',  # Alternative epic link field
                'customfield_10009',  # Alternative epic link field
                'customfield_10010',  # Alternative epic link field
                'customfield_10011',  # Alternative epic link field
                'customfield_10012',  # Alternative epic link field
                'customfield_10013',  # Alternative epic link field
                'customfield_10015',  # Alternative epic link field
                'customfield_10016',  # Alternative epic link field
                'parent'  # Some configurations use parent field
            ]

            # Try to find epic link in any of the common fields
            for field_name in epic_link_fields:
                try:
                    field_value = getattr(issue.fields, field_name, None)
                    if field_value:
                        # Handle different field value types
                        if hasattr(field_value, 'key'):
                            epic_link = field_value.key
                        elif isinstance(field_value, str):
                            epic_link = field_value

                        if epic_link:
                            break
                except Exception:
                    continue

            if not epic_link:
                return None

            # Fetch the epic issue with all fields
            epic_issue = self.jira.issue(epic_link, fields='*all')

            epic_name = epic_issue.fields.summary
            epic_description = getattr(epic_issue.fields, 'description', None)
            epic_acceptance_criteria = self._extract_acceptance_criteria(epic_issue)

            return JiraEpic(
                epic_key=epic_link,
                epic_name=epic_name,
                epic_description=epic_description,
                epic_acceptance_criteria=epic_acceptance_criteria
            )

        except Exception as e:
            logger.warning(f"Error fetching epic details for issue {issue.key}: {e}")
            return None

    def _get_attachments(self, issue) -> List[JiraAttachment]:
        """
        Get attachment details for a story.

        Args:
            issue: Jira issue object

        Returns:
            List[JiraAttachment]: List of attachment details
        """
        try:
            attachments = []

            # Get attachments from the issue
            if hasattr(issue.fields, 'attachment') and issue.fields.attachment:
                for attachment in issue.fields.attachment:
                    try:
                        # Determine if attachment is an image
                        is_image = self._is_image_attachment(attachment.mimeType)

                        # Get thumbnail URL for images
                        thumbnail_url = None
                        if is_image and hasattr(attachment, 'thumbnail'):
                            thumbnail_url = attachment.thumbnail

                        # Create attachment object
                        jira_attachment = JiraAttachment(
                            id=attachment.id,
                            filename=attachment.filename,
                            size=attachment.size,
                            mimeType=attachment.mimeType,
                            created=attachment.created,
                            author=attachment.author.displayName if hasattr(attachment.author, 'displayName') else str(attachment.author),
                            content_url=attachment.content,
                            thumbnail_url=thumbnail_url,
                            is_image=is_image
                        )

                        attachments.append(jira_attachment)

                    except Exception as attachment_error:
                        logger.warning(f"Error processing attachment {getattr(attachment, 'id', 'unknown')} for issue {issue.key}: {attachment_error}")
                        continue

            return attachments

        except Exception as e:
            logger.warning(f"Error fetching attachments for issue {issue.key}: {e}")
            return []

    def _is_image_attachment(self, mime_type: str) -> bool:
        """
        Check if an attachment is an image based on its MIME type.

        Args:
            mime_type (str): The MIME type of the attachment

        Returns:
            bool: True if the attachment is an image
        """
        image_mime_types = [
            'image/jpeg',
            'image/jpg',
            'image/png',
            'image/gif',
            'image/bmp',
            'image/webp',
            'image/svg+xml',
            'image/tiff'
        ]

        return mime_type.lower() in image_mime_types

    def get_story_by_id(self, story_id: str) -> JiraStory:
        """
        Fetch a single story by its ID with full details.

        Args:
            story_id (str): The Jira story ID (e.g., 'PROJ-123', 'SPUR-4')

        Returns:
            JiraStory: Complete story details with epic and attachment information
        """
        try:
            # Fetch the issue with all fields including attachments
            issue = self.jira.issue(story_id, fields='*all,attachment')

            # Convert issue to story
            story = self._convert_issue_to_story(issue)

            return story

        except Exception as e:
            logger.error(f"Error fetching story {story_id}: {e}")
            raise

    def get_projects(self) -> List[Dict[str, str]]:
        """
        Get list of available projects.

        Returns:
            List[Dict[str, str]]: List of projects with key and name
        """
        try:
            projects = self.jira.projects()
            return [{"key": project.key, "name": project.name} for project in projects]
        except Exception as e:
            logger.error(f"Error fetching projects: {e}")
            raise
