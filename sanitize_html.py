import bleach
from typing import Dict, List, Union, Optional, Any, Pattern
import re

# Define types similar to TypeScript types
SanitizeStylesType = Optional[Dict[str, Dict[str, List[Pattern]]]]
SanitizeClassListType = Optional[Dict[str, Union[bool, List[Union[str, Pattern]]]]]

# Default tags to allow in sanitization
DEFAULT_SANITIZE_TAGS = list(bleach.sanitizer.ALLOWED_TAGS) + [
    "body", "button", "form", "img", "input", "select", "textarea", "option",
]

DEFAULT_SANITIZE_STYLES: SanitizeStylesType = None
DEFAULT_SANITIZE_CLASS_LIST: SanitizeClassListType = None

def get_sanitize_options() -> Dict[str, Any]:
    """
    Returns the sanitization options to use with bleach.
    Equivalent to getSanitizeOptions() in the TypeScript version.
    """
    return {
        # The default allowed_tags list already includes commonly used tags
        "tags": DEFAULT_SANITIZE_TAGS,
        # Allow all attributes for all tags
        "attributes": {tag: ["*"] for tag in DEFAULT_SANITIZE_TAGS},
        # These don't directly map to bleach options but included for API similarity
        "styles": DEFAULT_SANITIZE_STYLES,
        "classes": DEFAULT_SANITIZE_CLASS_LIST,
    }

def sanitize_html(subject: str) -> str:
    """
    The reason for sanitization is because OpenAI does not need all of the HTML tags
    to know how to interpret the website, e.g. it will not make a difference to AI if
    we include or exclude <script> tags as they do not impact the already rendered DOM.

    In my experience, reducing HTML only to basic tags produces faster and more reliable prompts.

    Note that the output of this function is designed to interpret only the HTML tags.
    For instructions that rely on visual cues (e.g. "click red button") we intend to
    combine HTML with screenshots in the future versions of this library.
    """
    options = get_sanitize_options()
    return bleach.clean(
        subject,
        tags=options["tags"],
        attributes=options["attributes"],
        strip=True
    )
