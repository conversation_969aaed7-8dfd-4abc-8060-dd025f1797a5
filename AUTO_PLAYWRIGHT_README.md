# Auto Playwright for WindQ

This is a Python implementation of the [lucgagan/auto-playwright](https://github.com/lucgagan/auto-playwright) concept, adapted for the WindQ backend system. It allows you to write web automation tests using natural language instead of complex selectors.

## 🌟 Features

- **Natural Language Automation**: Write tests using plain English descriptions
- **AI-Powered Element Finding**: Uses OpenAI to understand and locate web elements
- **Auto-Healing Selectors**: Automatically adapts when page elements change
- **Integrated with WindQ**: Works seamlessly with your existing test framework
- **Multiple Action Types**: Supports clicks, form filling, navigation, and assertions

## 🚀 Quick Start

### 1. Basic Usage

```python
from auto_playwright import <PERSON><PERSON><PERSON>wright
from browser_manager import BrowserManager
from llm_service import LLMService

# Initialize components
browser_manager = BrowserManager()
browser_manager.launch_browser()
page = browser_manager.get_page()

llm_service = LLMService(api_key="your-openai-api-key")
auto_pw = AutoPlaywright(page, llm_service, debug=True)

# Use natural language to interact with the page
auto_pw.auto("Go to https://example.com")
auto_pw.auto("Click the login button")
auto_pw.auto("Fill the email field with '<EMAIL>'")
auto_pw.auto("Fill the password field with 'password123'")
auto_pw.auto("Click the submit button")
auto_pw.auto("Verify that 'Welcome' text is visible")
```

### 2. API Endpoints

The WindQ backend now includes auto-playwright endpoints:

#### Execute Single Task
```bash
POST /auto-playwright/execute
{
    "task": "Click the login button",
    "url": "https://example.com",  # optional
    "debug": false                 # optional
}
```

#### Create Test from Natural Language
```bash
POST /auto-playwright/create-test-from-natural-language
{
    "name": "Login Test",
    "description": "Test user login functionality",
    "tasks": [
        "Go to https://example.com",
        "Click the login button",
        "Fill in email with '<EMAIL>'",
        "Fill in password with 'password123'",
        "Click submit",
        "Verify that 'Welcome' text is visible"
    ]
}
```

## 📝 Supported Actions

### Navigation
- `"Go to https://example.com"`
- `"Navigate to the homepage"`
- `"Visit the login page"`

### Clicking
- `"Click the login button"`
- `"Click on the 'Submit' link"`
- `"Press the save button"`

### Form Filling
- `"Fill the email field with '<EMAIL>'"`
- `"Type 'John Doe' in the name input"`
- `"Enter 'password123' in the password field"`

### Dropdown Selection
- `"Select 'United States' from the country dropdown"`
- `"Choose 'Medium' from the size options"`

### Verification/Assertions
- `"Check if the page contains 'Welcome' text"`
- `"Verify that the login button is visible"`
- `"Confirm the page title is 'Dashboard'"`

### Information Extraction
- `"Get the text from the main heading"`
- `"Extract the error message"`
- `"Find the current page title"`

## 🔧 Configuration

### Environment Variables
```bash
# Required
OPENAI_API_KEY=your-openai-api-key-here

# Optional
PLAYWRIGHT_HEADLESS=false  # Set to true for headless mode
```

### Debug Mode
Enable debug mode to see detailed logs of what the AI is doing:

```python
auto_pw = AutoPlaywright(page, llm_service, debug=True)
```

## 🧪 Testing

Run the test suite to verify auto-playwright functionality:

```bash
python test_auto_playwright.py
```

This will run tests for:
- Basic navigation and element interaction
- Form filling and submission
- Search functionality
- Content verification

## 🏗️ Architecture

### Core Components

1. **AutoPlaywright**: Main class that processes natural language and executes actions
2. **LLM Integration**: Uses OpenAI to understand natural language and generate actions
3. **Element Finding**: Smart element location using multiple strategies
4. **Action Execution**: Converts AI decisions into Playwright actions

### How It Works

1. **Input Processing**: Natural language task is received
2. **Page Analysis**: Current page HTML is analyzed and sanitized
3. **AI Planning**: OpenAI determines the best approach to complete the task
4. **Element Location**: Elements are found using CSS selectors, text, or ARIA roles
5. **Action Execution**: Playwright performs the actual browser interactions
6. **Result Reporting**: Success/failure and any extracted data is returned

## 🔄 Integration with WindQ

Auto-playwright integrates seamlessly with WindQ's existing test framework:

### New Action Types
- `auto_click`: Natural language clicking
- `auto_fill`: Natural language form filling
- `auto_assert`: Natural language assertions
- `auto_task`: Generic natural language tasks

### Example Test Case
```json
{
    "name": "Login Test with Auto Playwright",
    "steps": [
        {
            "action": "goto",
            "url": "https://example.com"
        },
        {
            "action": "auto_click",
            "element_description": "login button",
            "natural_language": "Click the login button"
        },
        {
            "action": "auto_fill",
            "field_description": "email field",
            "value": "<EMAIL>",
            "natural_language": "Fill <NAME_EMAIL>"
        },
        {
            "action": "auto_assert",
            "assertion_description": "welcome message",
            "natural_language": "Verify that welcome message is visible"
        }
    ]
}
```

## 🎯 Best Practices

### Writing Effective Natural Language Commands

1. **Be Specific**: Instead of "click button", use "click the submit button"
2. **Use Visual Cues**: Reference text, colors, or positions when helpful
3. **One Action Per Command**: Keep each command focused on a single action
4. **Include Context**: Mention the type of element when possible

### Good Examples
✅ `"Click the blue 'Submit' button at the bottom of the form"`
✅ `"Fill the email input field with '<EMAIL>'"`
✅ `"Select 'Premium' from the subscription plan dropdown"`

### Avoid
❌ `"Click something"`
❌ `"Fill stuff"`
❌ `"Do the login thing"`

## 🐛 Troubleshooting

### Common Issues

1. **Element Not Found**
   - Make sure your description is specific enough
   - Check if the element is visible on the page
   - Try using different descriptive terms

2. **API Key Issues**
   - Verify your OpenAI API key is set correctly
   - Check that you have sufficient API credits

3. **Timeout Errors**
   - Some pages may take longer to load
   - Consider adding wait conditions before actions

### Debug Tips

1. Enable debug mode to see AI reasoning
2. Check the browser console for JavaScript errors
3. Verify the page has fully loaded before issuing commands
4. Use the test script to validate basic functionality

## 📚 Examples

See `test_auto_playwright.py` for comprehensive examples of:
- Basic navigation and interaction
- Form filling and submission
- Search functionality
- Content verification and extraction

## 🤝 Contributing

This implementation is based on the excellent work by [lucgagan/auto-playwright](https://github.com/lucgagan/auto-playwright). 

To contribute:
1. Test the functionality with different websites
2. Report issues with specific natural language commands
3. Suggest improvements for element finding accuracy
4. Add new test cases for edge scenarios

## 📄 License

This implementation follows the same principles as the original auto-playwright project and is designed to work within the WindQ testing framework.
