<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WindQ Token Usage Dashboard</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        .stat-card h3 {
            margin: 0 0 10px 0;
            font-size: 1.2em;
        }
        .stat-card .number {
            font-size: 2.5em;
            font-weight: bold;
            margin: 10px 0;
        }
        .stat-card .label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        .activity-log {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        .activity-log h3 {
            margin-top: 0;
            color: #333;
        }
        .log-entry {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #667eea;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .log-entry .action {
            font-weight: bold;
            color: #333;
        }
        .log-entry .tokens {
            background: #667eea;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9em;
        }
        .test-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
        }
        .btn:hover {
            background: #5a6fd8;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            text-align: center;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 WindQ Token Usage Dashboard</h1>
        
        <div class="stats-grid">
            <div class="stat-card">
                <h3>Total Tokens Used</h3>
                <div class="number" id="totalTokens">0</div>
                <div class="label">Across all operations</div>
            </div>
            <div class="stat-card">
                <h3>Parsing Tokens</h3>
                <div class="number" id="parsingTokens">0</div>
                <div class="label">Test case creation</div>
            </div>
            <div class="stat-card">
                <h3>Execution Tokens</h3>
                <div class="number" id="executionTokens">0</div>
                <div class="label">Test case runs</div>
            </div>
            <div class="stat-card">
                <h3>Average per Test</h3>
                <div class="number" id="averageTokens">0</div>
                <div class="label">Tokens per execution</div>
            </div>
        </div>

        <div class="test-controls">
            <button class="btn" onclick="createSampleTest()">Create Sample Test</button>
            <button class="btn" onclick="runLatestTest()" id="runBtn" disabled>Run Latest Test</button>
            <button class="btn" onclick="clearStats()">Clear Stats</button>
            <button class="btn" onclick="refreshStats()">Refresh</button>
        </div>

        <div id="status"></div>

        <div class="activity-log">
            <h3>📊 Recent Token Usage Activity</h3>
            <div id="activityLog">
                <div class="log-entry">
                    <div>
                        <div class="action">Dashboard Loaded</div>
                        <div style="font-size: 0.9em; color: #666;">Ready to track token usage</div>
                    </div>
                    <div class="tokens">0 tokens</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let stats = {
            totalTokens: 0,
            parsingTokens: 0,
            executionTokens: 0,
            testCount: 0,
            latestTestId: null
        };

        function updateDisplay() {
            document.getElementById('totalTokens').textContent = stats.totalTokens;
            document.getElementById('parsingTokens').textContent = stats.parsingTokens;
            document.getElementById('executionTokens').textContent = stats.executionTokens;
            document.getElementById('averageTokens').textContent = 
                stats.testCount > 0 ? Math.round(stats.executionTokens / stats.testCount) : 0;
            
            document.getElementById('runBtn').disabled = !stats.latestTestId;
        }

        function addLogEntry(action, tokens, details = '') {
            const log = document.getElementById('activityLog');
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            entry.innerHTML = `
                <div>
                    <div class="action">${action}</div>
                    <div style="font-size: 0.9em; color: #666;">${details}</div>
                </div>
                <div class="tokens">${tokens} tokens</div>
            `;
            log.insertBefore(entry, log.firstChild);
            
            // Keep only last 10 entries
            while (log.children.length > 10) {
                log.removeChild(log.lastChild);
            }
        }

        function showStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.className = `status ${type}`;
            status.textContent = message;
            setTimeout(() => status.textContent = '', 5000);
        }

        async function createSampleTest() {
            showStatus('Creating sample test case...', 'info');
            
            const sampleTest = {
                name: `Sample Test ${Date.now()}`,
                description: 'Auto-generated test for token tracking',
                natural_language_input: 'Go to https://example.com, fill the email <NAME_EMAIL>, and click the submit button'
            };

            try {
                const response = await fetch('http://localhost:8000/create-test-case', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(sampleTest)
                });

                if (response.ok) {
                    const result = await response.json();
                    const tokens = result.tokens_used_for_parsing || 0;
                    
                    stats.parsingTokens += tokens;
                    stats.totalTokens += tokens;
                    stats.latestTestId = result.test_id;
                    
                    updateDisplay();
                    addLogEntry('Test Case Created', tokens, `ID: ${result.test_id}`);
                    showStatus(`Test case created successfully! Used ${tokens} tokens.`, 'success');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                showStatus(`Error creating test: ${error.message}`, 'error');
            }
        }

        async function runLatestTest() {
            if (!stats.latestTestId) return;
            
            showStatus('Running test case...', 'info');
            
            try {
                const response = await fetch(`http://localhost:8000/run-test-case/${stats.latestTestId}`, {
                    method: 'POST'
                });

                if (response.ok) {
                    const result = await response.json();
                    const tokens = result.tokens_used || 0;
                    
                    stats.executionTokens += tokens;
                    stats.totalTokens += tokens;
                    stats.testCount += 1;
                    
                    updateDisplay();
                    addLogEntry('Test Case Executed', tokens, `ID: ${stats.latestTestId}`);
                    showStatus(`Test executed successfully! Used ${tokens} tokens.`, 'success');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                showStatus(`Error running test: ${error.message}`, 'error');
            }
        }

        function clearStats() {
            stats = {
                totalTokens: 0,
                parsingTokens: 0,
                executionTokens: 0,
                testCount: 0,
                latestTestId: null
            };
            updateDisplay();
            document.getElementById('activityLog').innerHTML = `
                <div class="log-entry">
                    <div>
                        <div class="action">Stats Cleared</div>
                        <div style="font-size: 0.9em; color: #666;">Token usage reset</div>
                    </div>
                    <div class="tokens">0 tokens</div>
                </div>
            `;
            showStatus('Statistics cleared!', 'info');
        }

        async function refreshStats() {
            showStatus('Refreshing statistics...', 'info');
            // In a real implementation, you'd fetch actual usage from the backend
            showStatus('Statistics refreshed!', 'success');
        }

        // Initialize display
        updateDisplay();
    </script>
</body>
</html>
