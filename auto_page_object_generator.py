import os
import j<PERSON>
from typing import Dict, List, Any, Optional
from playwright.sync_api import Page, Locator
from bs4 import BeautifulSoup
import re
from datetime import datetime


class AutoPageObjectGenerator:
    """
    Automatically generates Page Object Model classes by analyzing web pages.
    Extracts common elements, forms, and interactive components to create
    reusable page object classes.
    """

    def __init__(self, page: Page, output_dir: str = "generated_page_objects"):
        """
        Initialize the Auto Page Object Generator.

        Args:
            page (Page): The Playwright page object
            output_dir (str): Directory to save generated page objects
        """
        self.page = page
        self.output_dir = output_dir
        self.page_elements = {}
        self.page_metadata = {}
        
        # Ensure output directory exists
        os.makedirs(output_dir, exist_ok=True)

    def analyze_page(self, url: str = None) -> Dict[str, Any]:
        """
        Analyze the current page and extract elements for page object generation.

        Args:
            url (str, optional): URL to navigate to before analysis

        Returns:
            Dict[str, Any]: Analysis results containing elements and metadata
        """
        if url:
            self.page.goto(url)
            
        # Wait for page to load
        self.page.wait_for_load_state("networkidle")
        
        current_url = self.page.url
        page_title = self.page.title()
        
        print(f"Analyzing page: {current_url}")
        print(f"Page title: {page_title}")
        
        # Get page content
        html_content = self.page.content()
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Extract various element types
        elements = {
            'buttons': self._extract_buttons(),
            'inputs': self._extract_inputs(),
            'links': self._extract_links(),
            'forms': self._extract_forms(),
            'dropdowns': self._extract_dropdowns(),
            'checkboxes': self._extract_checkboxes(),
            'radio_buttons': self._extract_radio_buttons(),
            'tables': self._extract_tables(),
            'navigation': self._extract_navigation(),
            'modals': self._extract_modals()
        }
        
        # Store metadata
        self.page_metadata = {
            'url': current_url,
            'title': page_title,
            'analyzed_at': datetime.now().isoformat(),
            'element_counts': {k: len(v) for k, v in elements.items()}
        }
        
        self.page_elements = elements
        
        return {
            'metadata': self.page_metadata,
            'elements': elements
        }

    def _extract_buttons(self) -> List[Dict[str, Any]]:
        """Extract all button elements from the page."""
        buttons = []
        
        # Find buttons using multiple strategies
        button_selectors = [
            'button',
            'input[type="button"]',
            'input[type="submit"]',
            'input[type="reset"]',
            '[role="button"]',
            'a.btn, a.button',
            '.btn, .button'
        ]
        
        for selector in button_selectors:
            try:
                elements = self.page.locator(selector).all()
                for i, element in enumerate(elements):
                    try:
                        text = element.inner_text().strip()
                        if not text:
                            text = element.get_attribute('value') or element.get_attribute('aria-label') or f"Button_{i}"
                        
                        button_info = {
                            'name': self._generate_element_name(text, 'button'),
                            'selector': selector,
                            'text': text,
                            'id': element.get_attribute('id'),
                            'class': element.get_attribute('class'),
                            'type': element.get_attribute('type'),
                            'visible': element.is_visible(),
                            'enabled': element.is_enabled()
                        }
                        buttons.append(button_info)
                    except Exception as e:
                        print(f"Error extracting button info: {e}")
                        continue
            except Exception as e:
                print(f"Error with selector {selector}: {e}")
                continue
                
        return buttons

    def _extract_inputs(self) -> List[Dict[str, Any]]:
        """Extract all input elements from the page."""
        inputs = []
        
        input_selectors = [
            'input[type="text"]',
            'input[type="email"]',
            'input[type="password"]',
            'input[type="number"]',
            'input[type="tel"]',
            'input[type="url"]',
            'input[type="search"]',
            'textarea'
        ]
        
        for selector in input_selectors:
            try:
                elements = self.page.locator(selector).all()
                for element in elements:
                    try:
                        label_text = self._find_label_for_input(element)
                        placeholder = element.get_attribute('placeholder') or ''
                        
                        name = label_text or placeholder or element.get_attribute('name') or 'input'
                        
                        input_info = {
                            'name': self._generate_element_name(name, 'input'),
                            'selector': selector,
                            'label': label_text,
                            'placeholder': placeholder,
                            'id': element.get_attribute('id'),
                            'name_attr': element.get_attribute('name'),
                            'type': element.get_attribute('type'),
                            'required': element.get_attribute('required') is not None,
                            'visible': element.is_visible(),
                            'enabled': element.is_enabled()
                        }
                        inputs.append(input_info)
                    except Exception as e:
                        print(f"Error extracting input info: {e}")
                        continue
            except Exception as e:
                print(f"Error with selector {selector}: {e}")
                continue
                
        return inputs

    def _extract_links(self) -> List[Dict[str, Any]]:
        """Extract all link elements from the page."""
        links = []
        
        try:
            elements = self.page.locator('a[href]').all()
            for element in elements:
                try:
                    text = element.inner_text().strip()
                    href = element.get_attribute('href')
                    
                    if text and href:
                        link_info = {
                            'name': self._generate_element_name(text, 'link'),
                            'selector': 'a[href]',
                            'text': text,
                            'href': href,
                            'id': element.get_attribute('id'),
                            'class': element.get_attribute('class'),
                            'visible': element.is_visible()
                        }
                        links.append(link_info)
                except Exception as e:
                    print(f"Error extracting link info: {e}")
                    continue
        except Exception as e:
            print(f"Error extracting links: {e}")
            
        return links

    def _extract_forms(self) -> List[Dict[str, Any]]:
        """Extract all form elements from the page."""
        forms = []
        
        try:
            elements = self.page.locator('form').all()
            for i, element in enumerate(elements):
                try:
                    form_info = {
                        'name': f'form_{i + 1}',
                        'selector': 'form',
                        'id': element.get_attribute('id'),
                        'class': element.get_attribute('class'),
                        'action': element.get_attribute('action'),
                        'method': element.get_attribute('method') or 'GET',
                        'visible': element.is_visible()
                    }
                    forms.append(form_info)
                except Exception as e:
                    print(f"Error extracting form info: {e}")
                    continue
        except Exception as e:
            print(f"Error extracting forms: {e}")
            
        return forms

    def _extract_dropdowns(self) -> List[Dict[str, Any]]:
        """Extract all dropdown/select elements from the page."""
        dropdowns = []
        
        try:
            elements = self.page.locator('select').all()
            for element in elements:
                try:
                    label_text = self._find_label_for_input(element)
                    name = label_text or element.get_attribute('name') or 'dropdown'
                    
                    # Get options
                    options = []
                    option_elements = element.locator('option').all()
                    for option in option_elements:
                        options.append({
                            'text': option.inner_text(),
                            'value': option.get_attribute('value')
                        })
                    
                    dropdown_info = {
                        'name': self._generate_element_name(name, 'dropdown'),
                        'selector': 'select',
                        'label': label_text,
                        'id': element.get_attribute('id'),
                        'name_attr': element.get_attribute('name'),
                        'options': options,
                        'multiple': element.get_attribute('multiple') is not None,
                        'visible': element.is_visible(),
                        'enabled': element.is_enabled()
                    }
                    dropdowns.append(dropdown_info)
                except Exception as e:
                    print(f"Error extracting dropdown info: {e}")
                    continue
        except Exception as e:
            print(f"Error extracting dropdowns: {e}")
            
        return dropdowns

    def _extract_checkboxes(self) -> List[Dict[str, Any]]:
        """Extract all checkbox elements from the page."""
        checkboxes = []
        
        try:
            elements = self.page.locator('input[type="checkbox"]').all()
            for element in elements:
                try:
                    label_text = self._find_label_for_input(element)
                    name = label_text or element.get_attribute('name') or 'checkbox'
                    
                    checkbox_info = {
                        'name': self._generate_element_name(name, 'checkbox'),
                        'selector': 'input[type="checkbox"]',
                        'label': label_text,
                        'id': element.get_attribute('id'),
                        'name_attr': element.get_attribute('name'),
                        'value': element.get_attribute('value'),
                        'checked': element.is_checked(),
                        'visible': element.is_visible(),
                        'enabled': element.is_enabled()
                    }
                    checkboxes.append(checkbox_info)
                except Exception as e:
                    print(f"Error extracting checkbox info: {e}")
                    continue
        except Exception as e:
            print(f"Error extracting checkboxes: {e}")
            
        return checkboxes

    def _extract_radio_buttons(self) -> List[Dict[str, Any]]:
        """Extract all radio button elements from the page."""
        radio_buttons = []
        
        try:
            elements = self.page.locator('input[type="radio"]').all()
            for element in elements:
                try:
                    label_text = self._find_label_for_input(element)
                    name = label_text or element.get_attribute('name') or 'radio'
                    
                    radio_info = {
                        'name': self._generate_element_name(name, 'radio'),
                        'selector': 'input[type="radio"]',
                        'label': label_text,
                        'id': element.get_attribute('id'),
                        'name_attr': element.get_attribute('name'),
                        'value': element.get_attribute('value'),
                        'checked': element.is_checked(),
                        'visible': element.is_visible(),
                        'enabled': element.is_enabled()
                    }
                    radio_buttons.append(radio_info)
                except Exception as e:
                    print(f"Error extracting radio button info: {e}")
                    continue
        except Exception as e:
            print(f"Error extracting radio buttons: {e}")
            
        return radio_buttons

    def _extract_tables(self) -> List[Dict[str, Any]]:
        """Extract all table elements from the page."""
        tables = []
        
        try:
            elements = self.page.locator('table').all()
            for i, element in enumerate(elements):
                try:
                    # Get headers
                    headers = []
                    header_elements = element.locator('th').all()
                    for header in header_elements:
                        headers.append(header.inner_text().strip())
                    
                    # Count rows
                    row_count = element.locator('tr').count()
                    
                    table_info = {
                        'name': f'table_{i + 1}',
                        'selector': 'table',
                        'id': element.get_attribute('id'),
                        'class': element.get_attribute('class'),
                        'headers': headers,
                        'row_count': row_count,
                        'visible': element.is_visible()
                    }
                    tables.append(table_info)
                except Exception as e:
                    print(f"Error extracting table info: {e}")
                    continue
        except Exception as e:
            print(f"Error extracting tables: {e}")
            
        return tables

    def _extract_navigation(self) -> List[Dict[str, Any]]:
        """Extract navigation elements from the page."""
        navigation = []
        
        nav_selectors = ['nav', '.navbar', '.navigation', '.menu', '[role="navigation"]']
        
        for selector in nav_selectors:
            try:
                elements = self.page.locator(selector).all()
                for i, element in enumerate(elements):
                    try:
                        nav_info = {
                            'name': f'navigation_{i + 1}',
                            'selector': selector,
                            'id': element.get_attribute('id'),
                            'class': element.get_attribute('class'),
                            'visible': element.is_visible()
                        }
                        navigation.append(nav_info)
                    except Exception as e:
                        print(f"Error extracting navigation info: {e}")
                        continue
            except Exception as e:
                print(f"Error with navigation selector {selector}: {e}")
                continue
                
        return navigation

    def _extract_modals(self) -> List[Dict[str, Any]]:
        """Extract modal/dialog elements from the page."""
        modals = []
        
        modal_selectors = ['.modal', '.dialog', '[role="dialog"]', '[role="alertdialog"]']
        
        for selector in modal_selectors:
            try:
                elements = self.page.locator(selector).all()
                for i, element in enumerate(elements):
                    try:
                        modal_info = {
                            'name': f'modal_{i + 1}',
                            'selector': selector,
                            'id': element.get_attribute('id'),
                            'class': element.get_attribute('class'),
                            'visible': element.is_visible()
                        }
                        modals.append(modal_info)
                    except Exception as e:
                        print(f"Error extracting modal info: {e}")
                        continue
            except Exception as e:
                print(f"Error with modal selector {selector}: {e}")
                continue
                
        return modals

    def _find_label_for_input(self, element: Locator) -> str:
        """Find the label text for an input element."""
        try:
            # Try to find label by 'for' attribute
            element_id = element.get_attribute('id')
            if element_id:
                label = self.page.locator(f'label[for="{element_id}"]').first
                if label.count() > 0:
                    return label.inner_text().strip()
            
            # Try to find parent label
            parent_label = element.locator('xpath=ancestor::label[1]').first
            if parent_label.count() > 0:
                return parent_label.inner_text().strip()
                
            # Try to find preceding label
            preceding_label = element.locator('xpath=preceding-sibling::label[1]').first
            if preceding_label.count() > 0:
                return preceding_label.inner_text().strip()
                
        except Exception as e:
            print(f"Error finding label: {e}")
            
        return ""

    def _generate_element_name(self, text: str, element_type: str) -> str:
        """Generate a valid Python variable name from element text."""
        # Clean the text
        name = re.sub(r'[^a-zA-Z0-9\s]', '', text)
        name = re.sub(r'\s+', '_', name.strip())
        name = name.lower()
        
        # Ensure it starts with a letter
        if name and not name[0].isalpha():
            name = f"{element_type}_{name}"
        elif not name:
            name = element_type
            
        # Ensure it's not too long
        if len(name) > 50:
            name = name[:50]
            
        return name

    def generate_page_object_class(self, class_name: str = None) -> str:
        """
        Generate a Page Object Model class based on analyzed elements.

        Args:
            class_name (str, optional): Name for the generated class

        Returns:
            str: Generated Python class code
        """
        if not self.page_elements:
            raise ValueError("No page elements analyzed. Call analyze_page() first.")

        if not class_name:
            # Generate class name from page title or URL
            title = self.page_metadata.get('title', '')
            if title:
                class_name = self._generate_class_name(title)
            else:
                class_name = "GeneratedPage"

        # Generate class code
        class_code = self._generate_class_header(class_name)
        class_code += self._generate_constructor()
        class_code += self._generate_element_properties()
        class_code += self._generate_action_methods()
        class_code += self._generate_assertion_methods()

        return class_code

    def _generate_class_name(self, title: str) -> str:
        """Generate a valid class name from page title."""
        name = re.sub(r'[^a-zA-Z0-9\s]', '', title)
        name = re.sub(r'\s+', '', name.title())

        if not name or not name[0].isalpha():
            name = "GeneratedPage"

        return name + "Page"

    def _generate_class_header(self, class_name: str) -> str:
        """Generate the class header and imports."""
        url = self.page_metadata.get('url', '')
        title = self.page_metadata.get('title', '')
        analyzed_at = self.page_metadata.get('analyzed_at', '')

        return f'''"""
Auto-generated Page Object Model for {title}
URL: {url}
Generated at: {analyzed_at}
"""

from playwright.sync_api import Page, Locator, expect
from typing import Optional


class {class_name}:
    """
    Page Object Model for {title}
    Auto-generated from page analysis
    """

'''

    def _generate_constructor(self) -> str:
        """Generate the class constructor."""
        url = self.page_metadata.get('url', '')

        return f'''    def __init__(self, page: Page):
        """
        Initialize the page object.

        Args:
            page (Page): Playwright page instance
        """
        self.page = page
        self.url = "{url}"

'''

    def _generate_element_properties(self) -> str:
        """Generate properties for all page elements."""
        code = "    # Element Locators\n"

        # Generate properties for each element type
        for element_type, elements in self.page_elements.items():
            if elements:
                code += f"\n    # {element_type.title()}\n"
                for element in elements:
                    property_code = self._generate_element_property(element, element_type)
                    code += property_code

        return code + "\n"

    def _generate_element_property(self, element: Dict[str, Any], element_type: str) -> str:
        """Generate a property for a single element."""
        name = element.get('name', 'element')
        selector = self._get_best_selector(element)

        return f'''    @property
    def {name}(self) -> Locator:
        """Get the {element.get('text', element.get('label', name))} {element_type}."""
        return self.page.locator("{selector}")

'''

    def _get_best_selector(self, element: Dict[str, Any]) -> str:
        """Determine the best selector for an element."""
        # Priority: ID > Name > Class > Text > Generic selector
        if element.get('id'):
            return f"#{element['id']}"
        elif element.get('name_attr'):
            return f"[name='{element['name_attr']}']"
        elif element.get('text') and len(element['text']) < 50:
            return f"text={element['text']}"
        else:
            return element.get('selector', 'unknown')

    def _generate_action_methods(self) -> str:
        """Generate action methods for interactive elements."""
        code = "    # Action Methods\n"

        # Generate methods for buttons
        buttons = self.page_elements.get('buttons', [])
        for button in buttons:
            method_name = f"click_{button['name']}"
            code += f'''
    def {method_name}(self):
        """Click the {button.get('text', button['name'])} button."""
        self.{button['name']}.click()
        return self
'''

        # Generate methods for inputs
        inputs = self.page_elements.get('inputs', [])
        for input_elem in inputs:
            method_name = f"fill_{input_elem['name']}"
            code += f'''
    def {method_name}(self, value: str):
        """Fill the {input_elem.get('label', input_elem['name'])} input."""
        self.{input_elem['name']}.fill(value)
        return self
'''

        # Generate methods for dropdowns
        dropdowns = self.page_elements.get('dropdowns', [])
        for dropdown in dropdowns:
            method_name = f"select_{dropdown['name']}"
            code += f'''
    def {method_name}(self, value: str):
        """Select option in {dropdown.get('label', dropdown['name'])} dropdown."""
        self.{dropdown['name']}.select_option(value)
        return self
'''

        # Generate methods for checkboxes
        checkboxes = self.page_elements.get('checkboxes', [])
        for checkbox in checkboxes:
            check_method = f"check_{checkbox['name']}"
            uncheck_method = f"uncheck_{checkbox['name']}"
            code += f'''
    def {check_method}(self):
        """Check the {checkbox.get('label', checkbox['name'])} checkbox."""
        self.{checkbox['name']}.check()
        return self

    def {uncheck_method}(self):
        """Uncheck the {checkbox.get('label', checkbox['name'])} checkbox."""
        self.{checkbox['name']}.uncheck()
        return self
'''

        return code

    def _generate_assertion_methods(self) -> str:
        """Generate assertion methods for page verification."""
        code = "\n    # Assertion Methods\n"

        code += f'''
    def should_be_loaded(self):
        """Assert that the page is loaded correctly."""
        expect(self.page).to_have_url("{self.page_metadata.get('url', '')}")
        expect(self.page).to_have_title("{self.page_metadata.get('title', '')}")
        return self

    def should_be_visible(self):
        """Assert that key page elements are visible."""
'''

        # Add visibility assertions for key elements
        for element_type, elements in self.page_elements.items():
            if elements and element_type in ['buttons', 'inputs', 'forms']:
                for element in elements[:3]:  # Limit to first 3 elements
                    if element.get('visible', True):
                        code += f"        expect(self.{element['name']}).to_be_visible()\n"

        code += "        return self\n"

        return code

    def save_page_object_class(self, class_name: str = None, filename: str = None) -> str:
        """
        Generate and save a Page Object Model class to a file.

        Args:
            class_name (str, optional): Name for the generated class
            filename (str, optional): Filename to save the class

        Returns:
            str: Path to the saved file
        """
        class_code = self.generate_page_object_class(class_name)

        if not filename:
            if class_name:
                filename = f"{class_name.lower()}.py"
            else:
                filename = "generated_page.py"

        filepath = os.path.join(self.output_dir, filename)

        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(class_code)

        print(f"Page Object Model saved to: {filepath}")
        return filepath

    def save_analysis_report(self, filename: str = None) -> str:
        """
        Save the page analysis results to a JSON file.

        Args:
            filename (str, optional): Filename for the report

        Returns:
            str: Path to the saved report
        """
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"page_analysis_{timestamp}.json"

        filepath = os.path.join(self.output_dir, filename)

        report_data = {
            'metadata': self.page_metadata,
            'elements': self.page_elements,
            'summary': {
                'total_elements': sum(len(elements) for elements in self.page_elements.values()),
                'element_breakdown': {k: len(v) for k, v in self.page_elements.items()}
            }
        }

        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)

        print(f"Analysis report saved to: {filepath}")
        return filepath
