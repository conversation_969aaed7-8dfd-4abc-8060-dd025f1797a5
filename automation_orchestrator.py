import os
from typing import List, Dict, Any

from llm_service import LLMService
from browser_manager import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from instruction_parser import Instruction<PERSON>arser
from action_executor import ActionExecutor


class AutomationOrchestrator:
    """
    Orchestrates the web automation process, combining LLM parsing,
    browser interaction, element finding, and action execution.
    This class replaces the original WebTestAutomation.
    """

    def __init__(self, api_key: str = None, init_browser: bool = False, model: str = "gpt-3.5-turbo"):
        """
        Initializes the AutomationOrchestrator.

        Args:
            api_key (str, optional): OpenAI API key. Defaults to None.
            init_browser (bool, optional): Whether to initialize the browser immediately. Defaults to False.
            model (str, optional): The OpenAI model to use. Defaults to "gpt-3.5-turbo".
        """
        self.llm_service = LLMService(api_key=api_key, model=model)
        self.browser_manager = BrowserManager(headless=False)  # Set to True for headless in production
        self.instruction_parser = InstructionParser(llm_service=self.llm_service)
        self.action_executor = None  # Initialized after browser launch (uses auto-playwright)

        if init_browser:
            self.initialize_browser_components()

    def initialize_browser_components(self):
        """
        Launches the browser and initializes components that depend on the page object.
        """
        if not self.browser_manager.page:
            self.browser_manager.launch_browser()
            page = self.browser_manager.get_page()
            # Note: ElementFinder is no longer needed as ActionExecutor uses auto-playwright
            self.action_executor = ActionExecutor(page=page)
            print("Browser-dependent components initialized with auto-playwright.")
        else:
            print("Browser components already initialized.")

    def parse_natural_language_instructions(self, nl_input: str) -> List[Dict[str, Any]]:
        """
        Parses natural language input into a list of structured automation steps.

        Args:
            nl_input (str): The natural language instructions.

        Returns:
            List[Dict[str, Any]]: A list of structured action dictionaries.
        """
        return self.instruction_parser.parse_instructions(nl_input)

    def execute_instructions(self, instructions: List[Dict[str, Any]], screenshots_dir: str) -> List[Dict[str, Any]]:
        """
        Executes a list of structured automation instructions.

        Args:
            instructions (List[Dict[str, Any]]): A list of structured action dictionaries.
            screenshots_dir (str): Directory to save screenshots after each step.

        Returns:
            List[Dict[str, Any]]: A list of results for each executed action.
        """
        if not self.browser_manager.page or not self.action_executor:
            self.initialize_browser_components()

        results = []
        os.makedirs(screenshots_dir, exist_ok=True)

        for idx, instruction in enumerate(instructions):
            action_type = instruction.get("action", "unknown")
            print(f"\nExecuting step {idx + 1}: {action_type} - {instruction}")
            step_result = self.action_executor.execute_action(instruction)

            # Add screenshot path to the result
            screenshot_path = f"{screenshots_dir}/step_{idx + 1}_{action_type}.png"
            try:
                self.browser_manager.get_page().screenshot(path=screenshot_path)
                step_result["details"]["screenshot"] = screenshot_path
                print(f"Screenshot saved: {screenshot_path}")
            except Exception as e:
                print(f"Failed to save screenshot for step {idx + 1}: {e}")
                step_result["details"]["screenshot_error"] = str(e)

            results.append(step_result)

        return results

    def get_token_usage(self) -> int:
        """
        Returns the total token usage from the LLM service.

        Returns:
            int: Total tokens used.
        """
        return self.llm_service.get_total_token_usage()

    def close(self):
        """
        Closes the browser and Playwright resources.
        """
        self.browser_manager.close()

