import time
import json
from datetime import datetime
from typing import Dict, List, Any, Optional
from playwright.sync_api import Page, Response
import statistics
import os


class AutoPerformanceMonitor:
    """
    Automatically monitors and analyzes web page performance metrics,
    including load times, network requests, and resource usage.
    """

    def __init__(self, page: Page, output_dir: str = "performance_reports"):
        """
        Initialize the Auto Performance Monitor.

        Args:
            page (Page): Playwright page instance
            output_dir (str): Directory to save performance reports
        """
        self.page = page
        self.output_dir = output_dir
        self.metrics = {}
        self.network_metrics = []
        self.timing_metrics = {}
        self.resource_metrics = {}
        self.monitoring_active = False
        self.start_time = None
        
        # Performance thresholds (configurable)
        self.thresholds = {
            "page_load_time": 3.0,  # seconds
            "first_contentful_paint": 2.0,  # seconds
            "largest_contentful_paint": 2.5,  # seconds
            "cumulative_layout_shift": 0.1,
            "first_input_delay": 0.1,  # seconds
            "total_blocking_time": 0.3,  # seconds
            "network_request_count": 50,
            "total_transfer_size": 2 * 1024 * 1024  # 2MB
        }
        
        # Ensure output directory exists
        os.makedirs(output_dir, exist_ok=True)
        
        # Setup event listeners
        self._setup_performance_listeners()

    def _setup_performance_listeners(self):
        """Setup event listeners for performance monitoring."""
        # Listen to network events
        self.page.on("request", self._on_request)
        self.page.on("response", self._on_response)
        
        # Listen to page events
        self.page.on("load", self._on_page_load)
        self.page.on("domcontentloaded", self._on_dom_ready)

    def start_monitoring(self, test_name: str = None):
        """
        Start performance monitoring for a test session.

        Args:
            test_name (str, optional): Name for the monitoring session
        """
        self.monitoring_active = True
        self.start_time = time.time()
        self.metrics = {
            "test_name": test_name or f"perf_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "start_time": datetime.now().isoformat(),
            "page_loads": [],
            "network_summary": {},
            "performance_scores": {},
            "violations": []
        }
        self.network_metrics = []
        self.timing_metrics = {}
        self.resource_metrics = {}
        
        print(f"Started performance monitoring: {self.metrics['test_name']}")

    def stop_monitoring(self) -> Dict[str, Any]:
        """
        Stop performance monitoring and generate report.

        Returns:
            Dict[str, Any]: Complete performance report
        """
        if not self.monitoring_active:
            print("No monitoring session active")
            return {}
        
        self.monitoring_active = False
        end_time = time.time()
        duration = end_time - self.start_time
        
        # Finalize metrics
        self.metrics["end_time"] = datetime.now().isoformat()
        self.metrics["total_duration"] = duration
        self.metrics["network_summary"] = self._analyze_network_metrics()
        self.metrics["performance_scores"] = self._calculate_performance_scores()
        self.metrics["violations"] = self._check_performance_violations()
        
        print(f"Performance monitoring stopped. Duration: {duration:.2f}s")
        return self.metrics

    def measure_page_load(self, url: str, wait_for_selector: str = None) -> Dict[str, Any]:
        """
        Measure page load performance for a specific URL.

        Args:
            url (str): URL to load and measure
            wait_for_selector (str, optional): Selector to wait for before measuring

        Returns:
            Dict[str, Any]: Page load performance metrics
        """
        print(f"Measuring page load performance for: {url}")
        
        # Start timing
        load_start = time.time()
        
        # Navigate to page
        response = self.page.goto(url, wait_until="networkidle")
        
        # Wait for specific selector if provided
        if wait_for_selector:
            try:
                self.page.wait_for_selector(wait_for_selector, timeout=10000)
            except Exception as e:
                print(f"Warning: Could not find selector {wait_for_selector}: {e}")
        
        load_end = time.time()
        load_time = load_end - load_start
        
        # Get Web Vitals and performance metrics
        performance_metrics = self._get_web_vitals()
        navigation_timing = self._get_navigation_timing()
        resource_timing = self._get_resource_timing()
        
        page_load_data = {
            "url": url,
            "timestamp": datetime.now().isoformat(),
            "load_time": load_time,
            "response_status": response.status if response else None,
            "web_vitals": performance_metrics,
            "navigation_timing": navigation_timing,
            "resource_timing": resource_timing,
            "page_size": self._calculate_page_size()
        }
        
        if self.monitoring_active:
            self.metrics["page_loads"].append(page_load_data)
        
        return page_load_data

    def _get_web_vitals(self) -> Dict[str, float]:
        """Get Core Web Vitals metrics from the page."""
        try:
            # Inject Web Vitals measurement script
            web_vitals_script = """
            () => {
                return new Promise((resolve) => {
                    const vitals = {};
                    
                    // Get performance entries
                    const perfEntries = performance.getEntriesByType('navigation')[0];
                    if (perfEntries) {
                        vitals.domContentLoaded = perfEntries.domContentLoadedEventEnd - perfEntries.domContentLoadedEventStart;
                        vitals.loadComplete = perfEntries.loadEventEnd - perfEntries.loadEventStart;
                    }
                    
                    // Get paint metrics
                    const paintEntries = performance.getEntriesByType('paint');
                    paintEntries.forEach(entry => {
                        if (entry.name === 'first-contentful-paint') {
                            vitals.firstContentfulPaint = entry.startTime / 1000;
                        }
                        if (entry.name === 'first-paint') {
                            vitals.firstPaint = entry.startTime / 1000;
                        }
                    });
                    
                    // Get LCP (Largest Contentful Paint)
                    const lcpEntries = performance.getEntriesByType('largest-contentful-paint');
                    if (lcpEntries.length > 0) {
                        vitals.largestContentfulPaint = lcpEntries[lcpEntries.length - 1].startTime / 1000;
                    }
                    
                    // Get CLS (Cumulative Layout Shift) - simplified
                    const clsEntries = performance.getEntriesByType('layout-shift');
                    let clsScore = 0;
                    clsEntries.forEach(entry => {
                        if (!entry.hadRecentInput) {
                            clsScore += entry.value;
                        }
                    });
                    vitals.cumulativeLayoutShift = clsScore;
                    
                    resolve(vitals);
                });
            }
            """
            
            return self.page.evaluate(web_vitals_script)
        except Exception as e:
            print(f"Error getting Web Vitals: {e}")
            return {}

    def _get_navigation_timing(self) -> Dict[str, float]:
        """Get navigation timing metrics."""
        try:
            timing_script = """
            () => {
                const timing = performance.timing;
                const navigation = performance.getEntriesByType('navigation')[0];
                
                return {
                    dnsLookup: timing.domainLookupEnd - timing.domainLookupStart,
                    tcpConnect: timing.connectEnd - timing.connectStart,
                    request: timing.responseStart - timing.requestStart,
                    response: timing.responseEnd - timing.responseStart,
                    domProcessing: timing.domComplete - timing.domLoading,
                    totalTime: timing.loadEventEnd - timing.navigationStart,
                    redirectTime: navigation ? navigation.redirectEnd - navigation.redirectStart : 0,
                    unloadTime: timing.unloadEventEnd - timing.unloadEventStart
                };
            }
            """
            
            return self.page.evaluate(timing_script)
        except Exception as e:
            print(f"Error getting navigation timing: {e}")
            return {}

    def _get_resource_timing(self) -> List[Dict[str, Any]]:
        """Get resource timing for all loaded resources."""
        try:
            resource_script = """
            () => {
                const resources = performance.getEntriesByType('resource');
                return resources.map(resource => ({
                    name: resource.name,
                    type: resource.initiatorType,
                    size: resource.transferSize || 0,
                    duration: resource.duration,
                    startTime: resource.startTime,
                    responseEnd: resource.responseEnd
                }));
            }
            """
            
            return self.page.evaluate(resource_script)
        except Exception as e:
            print(f"Error getting resource timing: {e}")
            return []

    def _calculate_page_size(self) -> Dict[str, int]:
        """Calculate total page size and resource breakdown."""
        try:
            size_script = """
            () => {
                const resources = performance.getEntriesByType('resource');
                const sizes = {
                    total: 0,
                    html: 0,
                    css: 0,
                    javascript: 0,
                    images: 0,
                    fonts: 0,
                    other: 0
                };
                
                resources.forEach(resource => {
                    const size = resource.transferSize || 0;
                    sizes.total += size;
                    
                    const type = resource.initiatorType;
                    if (type === 'navigation') {
                        sizes.html += size;
                    } else if (type === 'css' || resource.name.includes('.css')) {
                        sizes.css += size;
                    } else if (type === 'script' || resource.name.includes('.js')) {
                        sizes.javascript += size;
                    } else if (type === 'img' || /\.(jpg|jpeg|png|gif|webp|svg)$/i.test(resource.name)) {
                        sizes.images += size;
                    } else if (/\.(woff|woff2|ttf|otf|eot)$/i.test(resource.name)) {
                        sizes.fonts += size;
                    } else {
                        sizes.other += size;
                    }
                });
                
                return sizes;
            }
            """
            
            return self.page.evaluate(size_script)
        except Exception as e:
            print(f"Error calculating page size: {e}")
            return {"total": 0}

    def _on_request(self, request):
        """Handle network request events."""
        if self.monitoring_active:
            request_data = {
                "timestamp": time.time() - self.start_time,
                "type": "request",
                "url": request.url,
                "method": request.method,
                "resource_type": request.resource_type,
                "size": len(request.post_data or b"")
            }
            self.network_metrics.append(request_data)

    def _on_response(self, response: Response):
        """Handle network response events."""
        if self.monitoring_active:
            try:
                body_size = len(response.body()) if response.body() else 0
            except:
                body_size = 0
                
            response_data = {
                "timestamp": time.time() - self.start_time,
                "type": "response",
                "url": response.url,
                "status": response.status,
                "size": body_size,
                "headers": dict(response.headers)
            }
            self.network_metrics.append(response_data)

    def _on_page_load(self):
        """Handle page load events."""
        if self.monitoring_active:
            self.timing_metrics["page_load"] = time.time() - self.start_time

    def _on_dom_ready(self):
        """Handle DOM ready events."""
        if self.monitoring_active:
            self.timing_metrics["dom_ready"] = time.time() - self.start_time

    def _analyze_network_metrics(self) -> Dict[str, Any]:
        """Analyze collected network metrics."""
        if not self.network_metrics:
            return {}
        
        requests = [m for m in self.network_metrics if m["type"] == "request"]
        responses = [m for m in self.network_metrics if m["type"] == "response"]
        
        # Calculate response times
        response_times = []
        for request in requests:
            matching_response = next((r for r in responses if r["url"] == request["url"]), None)
            if matching_response:
                response_time = matching_response["timestamp"] - request["timestamp"]
                response_times.append(response_time)
        
        # Calculate sizes
        total_size = sum(r.get("size", 0) for r in responses)
        
        # Group by resource type
        resource_types = {}
        for request in requests:
            res_type = request.get("resource_type", "other")
            if res_type not in resource_types:
                resource_types[res_type] = {"count": 0, "size": 0}
            resource_types[res_type]["count"] += 1
        
        for response in responses:
            # Find matching request to get resource type
            matching_request = next((r for r in requests if r["url"] == response["url"]), None)
            if matching_request:
                res_type = matching_request.get("resource_type", "other")
                resource_types[res_type]["size"] += response.get("size", 0)
        
        return {
            "total_requests": len(requests),
            "total_responses": len(responses),
            "total_transfer_size": total_size,
            "average_response_time": statistics.mean(response_times) if response_times else 0,
            "median_response_time": statistics.median(response_times) if response_times else 0,
            "max_response_time": max(response_times) if response_times else 0,
            "resource_breakdown": resource_types,
            "failed_requests": len([r for r in responses if r["status"] >= 400])
        }

    def _calculate_performance_scores(self) -> Dict[str, float]:
        """Calculate performance scores based on collected metrics."""
        scores = {}
        
        # Page load score (0-100)
        if self.metrics.get("page_loads"):
            avg_load_time = statistics.mean([p["load_time"] for p in self.metrics["page_loads"]])
            load_score = max(0, 100 - (avg_load_time * 20))  # Penalty for slow loads
            scores["page_load_score"] = load_score
        
        # Network efficiency score
        network_summary = self.metrics.get("network_summary", {})
        if network_summary:
            request_count = network_summary.get("total_requests", 0)
            transfer_size = network_summary.get("total_transfer_size", 0)
            
            # Score based on request count and size
            request_score = max(0, 100 - (request_count * 2))  # Penalty for many requests
            size_score = max(0, 100 - (transfer_size / (1024 * 1024) * 10))  # Penalty for large transfers
            
            scores["network_efficiency_score"] = (request_score + size_score) / 2
        
        # Overall performance score
        if scores:
            scores["overall_score"] = statistics.mean(scores.values())
        
        return scores

    def _check_performance_violations(self) -> List[Dict[str, Any]]:
        """Check for performance threshold violations."""
        violations = []
        
        # Check page load times
        if self.metrics.get("page_loads"):
            for page_load in self.metrics["page_loads"]:
                load_time = page_load.get("load_time", 0)
                if load_time > self.thresholds["page_load_time"]:
                    violations.append({
                        "type": "page_load_time",
                        "threshold": self.thresholds["page_load_time"],
                        "actual": load_time,
                        "url": page_load.get("url", ""),
                        "severity": "high" if load_time > self.thresholds["page_load_time"] * 2 else "medium"
                    })
                
                # Check Web Vitals
                web_vitals = page_load.get("web_vitals", {})
                fcp = web_vitals.get("firstContentfulPaint", 0)
                if fcp > self.thresholds["first_contentful_paint"]:
                    violations.append({
                        "type": "first_contentful_paint",
                        "threshold": self.thresholds["first_contentful_paint"],
                        "actual": fcp,
                        "url": page_load.get("url", ""),
                        "severity": "medium"
                    })
                
                lcp = web_vitals.get("largestContentfulPaint", 0)
                if lcp > self.thresholds["largest_contentful_paint"]:
                    violations.append({
                        "type": "largest_contentful_paint",
                        "threshold": self.thresholds["largest_contentful_paint"],
                        "actual": lcp,
                        "url": page_load.get("url", ""),
                        "severity": "high"
                    })
                
                cls = web_vitals.get("cumulativeLayoutShift", 0)
                if cls > self.thresholds["cumulative_layout_shift"]:
                    violations.append({
                        "type": "cumulative_layout_shift",
                        "threshold": self.thresholds["cumulative_layout_shift"],
                        "actual": cls,
                        "url": page_load.get("url", ""),
                        "severity": "medium"
                    })
        
        # Check network metrics
        network_summary = self.metrics.get("network_summary", {})
        request_count = network_summary.get("total_requests", 0)
        if request_count > self.thresholds["network_request_count"]:
            violations.append({
                "type": "network_request_count",
                "threshold": self.thresholds["network_request_count"],
                "actual": request_count,
                "severity": "medium"
            })
        
        transfer_size = network_summary.get("total_transfer_size", 0)
        if transfer_size > self.thresholds["total_transfer_size"]:
            violations.append({
                "type": "total_transfer_size",
                "threshold": self.thresholds["total_transfer_size"],
                "actual": transfer_size,
                "severity": "high"
            })
        
        return violations

    def save_performance_report(self, filename: str = None) -> str:
        """
        Save performance report to a JSON file.

        Args:
            filename (str, optional): Filename for the report

        Returns:
            str: Path to the saved report
        """
        if not filename:
            test_name = self.metrics.get("test_name", "performance_test")
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{test_name}_{timestamp}.json"
        
        filepath = os.path.join(self.output_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(self.metrics, f, indent=2, ensure_ascii=False)
        
        print(f"Performance report saved to: {filepath}")
        return filepath

    def set_performance_thresholds(self, thresholds: Dict[str, float]):
        """
        Update performance thresholds.

        Args:
            thresholds (Dict[str, float]): New threshold values
        """
        self.thresholds.update(thresholds)
        print(f"Updated performance thresholds: {thresholds}")

    def get_performance_summary(self) -> Dict[str, Any]:
        """Get a summary of current performance metrics."""
        if not self.metrics:
            return {"status": "No monitoring data available"}
        
        summary = {
            "test_name": self.metrics.get("test_name", "Unknown"),
            "duration": self.metrics.get("total_duration", 0),
            "pages_tested": len(self.metrics.get("page_loads", [])),
            "performance_scores": self.metrics.get("performance_scores", {}),
            "violations_count": len(self.metrics.get("violations", [])),
            "network_requests": self.metrics.get("network_summary", {}).get("total_requests", 0),
            "total_transfer_size_mb": round(
                self.metrics.get("network_summary", {}).get("total_transfer_size", 0) / (1024 * 1024), 2
            )
        }
        
        return summary
