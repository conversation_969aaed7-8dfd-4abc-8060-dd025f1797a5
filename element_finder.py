import re
import json
from typing import Dict, Any
from playwright.sync_api import Page, Locator
from llm_service import LLMService
from sanitize_html import sanitize_html


class ElementFinder:
    """
    Responsible for finding elements on a Playwright page using a combination
    of heuristic strategies and LLM assistance.
    """

    def __init__(self, page: Page, llm_service: LLMService):
        """
        Initializes the ElementFinder.

        Args:
            page (Page): The Playwright page object to interact with.
            llm_service (LLMService): An instance of LLMService for LLM calls.
        """
        self.page = page
        self.llm_service = llm_service

    def _find_element_or_none(self, description: str,
                              detected_selector: Dict[str, Any] | None = None) -> Locator | None:
        """
        Finds an element based on its description, returning None if not found.
        Uses the detected_selector if provided.

        Args:
            description (str): A natural language description of the element.
            detected_selector (Dict[str, Any] | None): Previously detected selector.

        Returns:
            Locator | None: The Playwright Locator for the element, or None if not found.
        """
        try:
            # find_element now returns a tuple (Locator, used_selector_dict)
            element, _ = self.find_element(description, detected_selector=detected_selector)
            # Check if the locator actually points to a visible element
            if element and element.count() > 0:  # element itself can be Locator, ensure it's not None
                return element
            return None
        except Exception as e:
            print(f"Element not found for description '{description}': {e}")
            return None

    def find_element(self, description: str, detected_selector: Dict[str, Any] | None = None) -> tuple[
        Locator, Dict[str, Any]]:
        """
        Locates an element on the page based on its natural language description
        or a previously detected selector.

        Priority order:
        1. Previously detected selector (highest priority)
        2. Playwright built-in semantic locators (getByRole, getByLabel, etc.)
        3. Heuristic CSS selectors for common elements
        4. LLM-generated selectors
        5. Text matching (lowest priority)

        Args:
            description (str): A natural language description of the element.
            detected_selector (Dict[str, Any] | None): Optional. A dictionary with
                                                       "strategy" and "value" for a
                                                       previously detected selector.

        Returns:
            tuple[Locator, Dict[str, Any]]: A tuple containing:
                - Locator: A Playwright Locator object for the found element.
                - Dict[str, Any]: The selector dictionary {"strategy": str, "value": str}
                                  that was successfully used to find the element.
        Raises:
            Exception: If the element cannot be found using any strategy.
        """
        print(f"Attempting to find element: '{description}'")

        # 1. HIGHEST PRIORITY: Try detected_selector if provided
        if detected_selector and detected_selector.get("value"):
            strategy = detected_selector.get("strategy", "").lower()
            value = detected_selector.get("value")
            print(f"Attempting to use provided detected_selector: strategy='{strategy}', value='{value}'")
            try:
                locator = None
                if strategy == "css":
                    locator = self.page.locator(value)
                elif strategy == "text":
                    locator = self.page.locator(value)
                elif strategy == "role":
                    # Example: value could be "button" or "button, {'name': 'Login'}"
                    role_type = value
                    options = {}
                    if isinstance(value, str) and ", {" in value and value.endswith("}"):
                        try:
                            role_type_part, options_part = value.split(", {", 1)
                            role_type = role_type_part.strip()
                            options_str = "{" + options_part
                            # Use json.loads for safer parsing of dict-like strings
                            parsed_options = json.loads(options_str.replace("'", "\""))
                            if isinstance(parsed_options, dict):
                                for k, v_opt in parsed_options.items():
                                    if isinstance(v_opt, str) and v_opt.startswith("/") and (
                                            v_opt.endswith("/i") or v_opt.endswith("/")):
                                        flags = re.IGNORECASE if v_opt.endswith("/i") else 0
                                        pattern = v_opt[1:-2] if v_opt.endswith("/i") else v_opt[1:-1]
                                        options[k] = re.compile(pattern, flags)
                                    else:
                                        options[k] = v_opt
                        except Exception as parse_ex:
                            print(f"Could not parse role options '{value}': {parse_ex}. Using role type only.")
                            role_type = value.split(",")[0].strip()  # Fallback
                            options = {}

                    if options:
                        locator = self.page.get_by_role(role_type, **options)
                    else:  # Simple role type string
                        locator = self.page.get_by_role(role_type)

                elif strategy == "id":
                    locator = self.page.locator(f"#{value}")
                elif strategy == "xpath":
                    locator = self.page.locator(value)

                if locator and locator.count() > 0:
                    print(f"Found element using provided detected_selector: strategy='{strategy}', value='{value}'")
                    return locator.first, detected_selector
                else:
                    print(f"Provided detected_selector failed: strategy='{strategy}', value='{value}'")
            except Exception as e:
                print(f"Error using provided detected_selector (strategy='{strategy}', value='{value}'): {e}")
                # Fall through to other methods

        # 2. SECOND PRIORITY: Try Playwright's recommended built-in locators
        description_lower = description.lower()
        
        # Try getByRole for common interactive elements
        try:
            if "button" in description_lower:
                button_text = description_lower.replace("button", "").strip()
                locator = self.page.get_by_role("button", name=re.compile(button_text, re.IGNORECASE))
                if locator.count() > 0:
                    selector_info = {"strategy": "role", "value": f"button, {{\"name\": \"{button_text}\"}}"}
                    print(f"Found element using getByRole: {selector_info['value']}")
                    return locator.first, selector_info
            
            if "checkbox" in description_lower:
                locator = self.page.get_by_role("checkbox")
                if locator.count() > 0:
                    selector_info = {"strategy": "role", "value": "checkbox"}
                    print(f"Found element using getByRole: checkbox")
                    return locator.first, selector_info
            
            if "link" in description_lower or "anchor" in description_lower:
                link_text = description_lower.replace("link", "").replace("anchor", "").strip()
                locator = self.page.get_by_role("link", name=re.compile(link_text, re.IGNORECASE))
                if locator.count() > 0:
                    selector_info = {"strategy": "role", "value": f"link, {{\"name\": \"{link_text}\"}}"}
                    print(f"Found element using getByRole: {selector_info['value']}")
                    return locator.first, selector_info
        except Exception as e:
            print(f"Error trying getByRole locators: {e}")
        
        # Try getByLabel for form elements
        try:
            if any(keyword in description_lower for keyword in ["field", "input", "username", "email", "password"]):
                # Extract potential label text from description
                label_keywords = ["username", "email", "password", "name", "first name", "last name"]
                for keyword in label_keywords:
                    if keyword in description_lower:
                        locator = self.page.get_by_label(re.compile(keyword, re.IGNORECASE))
                        if locator.count() > 0:
                            selector_info = {"strategy": "label", "value": keyword}
                            print(f"Found element using getByLabel: {keyword}")
                            return locator.first, selector_info
        except Exception as e:
            print(f"Error trying getByLabel locators: {e}")
        
        # Try getByText for text elements
        try:
            # Extract potential text content from description
            # Remove common words like "element", "containing", "with", etc.
            text_keywords = description.split()
            if len(text_keywords) >= 1:
                for i in range(len(text_keywords)):
                    if i + 1 <= len(text_keywords):
                        text_to_try = " ".join(text_keywords[0:i+1])
                        if len(text_to_try) > 3:  # Don't try very short strings
                            locator = self.page.get_by_text(re.compile(text_to_try, re.IGNORECASE))
                            if locator.count() > 0:
                                selector_info = {"strategy": "text", "value": f"text=/{text_to_try}/i"}
                                print(f"Found element using getByText: {text_to_try}")
                                return locator.first, selector_info
        except Exception as e:
            print(f"Error trying getByText locators: {e}")
        
        # Try getByPlaceholder for input fields
        try:
            if any(keyword in description_lower for keyword in ["input", "field", "textbox", "textarea"]):
                for placeholder in ["email", "username", "password", "search", "name", "phone"]:
                    if placeholder in description_lower:
                        locator = self.page.get_by_placeholder(re.compile(placeholder, re.IGNORECASE))
                        if locator.count() > 0:
                            selector_info = {"strategy": "placeholder", "value": placeholder}
                            print(f"Found element using getByPlaceholder: {placeholder}")
                            return locator.first, selector_info
        except Exception as e:
            print(f"Error trying getByPlaceholder locators: {e}")

        # Try getByTitle
        try:
            locator = self.page.get_by_title(re.compile(description, re.IGNORECASE))
            if locator.count() > 0:
                selector_info = {"strategy": "title", "value": description}
                print(f"Found element using getByTitle: {description}")
                return locator.first, selector_info
        except Exception as e:
            print(f"Error trying getByTitle locators: {e}")

        # 3. THIRD PRIORITY: Heuristic approach for common elements (buttons, inputs)
        if "button" in description.lower():
            button_text = description.lower().replace("button", "").strip()
            heuristic_selectors = [
                f'button:has-text("{button_text}")', f'button:has-text("{button_text.capitalize()}")',
                f'[role="button"]:has-text("{button_text}")', f'[role="button"]:has-text("{button_text.capitalize()}")',
                f'.MuiButton-root:has-text("{button_text}")', f'.MuiButton-root:has-text("{button_text.capitalize()}")',
                f'button:text-is("{button_text}")', f'button:text-is("{button_text.capitalize()}")'
            ]
            for selector_value in heuristic_selectors:
                try:
                    locator = self.page.locator(selector_value)
                    if locator.count() > 0:
                        print(f"Found button using heuristic CSS selector: {selector_value}")
                        return locator.first, {"strategy": "css", "value": selector_value}
                except Exception:
                    continue

        field_description_lower = description.lower()
        if any(keyword in field_description_lower for keyword in
               ["username", "email", "password", "input", "field", "text"]):
            field_type = None
            if "username" in field_description_lower:
                field_type = "username"
            elif "email" in field_description_lower:
                field_type = "email"
            elif "password" in field_description_lower:
                field_type = "password"

            heuristic_input_selectors = [
                f"input[name*='{field_type}' i]" if field_type else None,
                f"input[id*='{field_type}' i]" if field_type else None,
                f"input[placeholder*='{field_type}' i]" if field_type else None,
                f"input[type='{field_type}']" if field_type else None,
                "input[type='text']", "input[type='email']", "input[type='password']",
                ".MuiInputBase-input", "[role='textbox']"
            ]
            heuristic_input_selectors = [s for s in heuristic_input_selectors if s]
            for selector_value in heuristic_input_selectors:
                try:
                    locator = self.page.locator(selector_value)
                    if locator.count() > 0:
                        print(f"Found input field using heuristic CSS selector: {selector_value}")
                        return locator.first, {"strategy": "css", "value": selector_value}
                except Exception:
                    continue

        # 4. FOURTH PRIORITY: LLM-based selector generation as a fallback
        print("Built-in locators and heuristic methods failed, trying LLM to find element.")
        page_html = sanitize_html(self.page.content())
        prompt = f"""
        I need to find an element described as: "{description}" on a webpage.
        Here's a snippet of the current HTML (it may be truncated):

        ```html
        {page_html[:10000]}
        ```

        Based on the description and HTML, give me the BEST Playwright selector.
        Return ONLY a JSON object with the following format:
        {{
            "strategy": "One of: css, text, role, id, xpath",
            "value": "The selector value (e.g., 'button.primary', 'text=\\"Submit\\"', 'button, {{\"name\": \"Login\"}}', 'user-id', '//div[@class=\\"item\\"]')"
        }}
        For 'role' strategy, if the role needs an accessible name or other attributes (like level for heading), include it in the value as a JSON string representation of a dictionary, e.g., 'button, {{\"name\": \"Login\"}}' or 'heading, {{\"name\": \"Welcome\", \"level\": 1}}'.
        For 'text' strategy, use 'text="Exact Text"' or 'text=/Regex/i'.
        CSS selectors should be valid.
        Choose the most specific and reliable selector.
        Respond with valid JSON only.
        """

        try:
            selector_info_str = self.llm_service.generate_content(prompt, response_format={"type": "json_object"})
            llm_selector_info = json.loads(selector_info_str)

            strategy = llm_selector_info.get("strategy", "").lower()
            value = llm_selector_info.get("value", "")

            if not value:
                raise ValueError("LLM returned an empty selector value.")

            locator = None
            if strategy == "css":
                locator = self.page.locator(value)
            elif strategy == "text":
                locator = self.page.locator(value)
            elif strategy == "role":
                role_type = value
                options = {}
                if isinstance(value, str) and ", {" in value and value.endswith("}"):
                    try:
                        role_type_part, options_part = value.split(", {", 1)
                        role_type = role_type_part.strip()
                        options_str = "{" + options_part
                        parsed_options = json.loads(options_str.replace("'", "\""))  # Basic replace for single quotes
                        if isinstance(parsed_options, dict):
                            for k, v_opt in parsed_options.items():
                                if isinstance(v_opt, str) and v_opt.startswith("/") and (
                                        v_opt.endswith("/i") or v_opt.endswith("/")):
                                    flags = re.IGNORECASE if v_opt.endswith("/i") else 0
                                    pattern = v_opt[1:-2] if v_opt.endswith("/i") else v_opt[1:-1]
                                    options[k] = re.compile(pattern, flags)
                                else:
                                    options[k] = v_opt
                    except Exception as parse_ex:
                        print(f"Could not parse LLM role options '{value}': {parse_ex}. Using role type only.")
                        role_type = value.split(",")[0].strip()  # Fallback
                        options = {}

                if options:
                    locator = self.page.get_by_role(role_type, **options)
                else:  # Simple role type string
                    locator = self.page.get_by_role(role_type)
            elif strategy == "id":
                locator = self.page.locator(f"#{value.strip()}")  # Ensure no leading/trailing spaces for ID
            elif strategy == "xpath":
                locator = self.page.locator(value)
            else:
                raise ValueError(f"Unknown LLM selector strategy: {strategy}")

            if locator and locator.count() > 0:
                print(f"Found element using LLM strategy '{strategy}' with value '{value}'")
                return locator.first, llm_selector_info
            else:
                print(f"LLM-generated selector strategy='{strategy}', value='{value}' did not find the element.")

        except Exception as e:
            print(f"Error using LLM to find element: {e}")
            # Fall through to final text fallback

        # 5. LOWEST PRIORITY: Final fallback: try by text content
        if description:
            exact_text_selector = f'text="{description}"'
            try:
                locator = self.page.locator(exact_text_selector)
                if locator.count() > 0:
                    print(f"Found element by exact text match: '{description}'")
                    return locator.first, {"strategy": "text", "value": exact_text_selector}
            except Exception:
                pass

            regex_text_selector = f'text=/{re.escape(description)}/i'
            try:
                locator = self.page.locator(regex_text_selector)
                if locator.count() > 0:
                    print(f"Found element by case-insensitive text match: '{description}'")
                    return locator.first, {"strategy": "text", "value": regex_text_selector}
            except Exception:
                pass

        raise Exception(f"Could not find element with description: '{description}' using any method.")
