import os
import openai
from typing import Dict, Any

class LLMService:
    """
    Manages interactions with the OpenAI API, including API key handling,
    model selection, and tracking token usage.
    """
    def __init__(self, api_key: str = None, model: str = "gpt-3.5-turbo"):
        """
        Initializes the LLMService.

        Args:
            api_key (str, optional): OpenAI API key. Defaults to None,
                                     in which case it tries to get from OPENAI_API_KEY env var.
            model (str, optional): The OpenAI model to use. Defaults to "gpt-3.5-turbo".
        """
        self.api_key = api_key or os.environ.get("OPENAI_API_KEY")
        if not self.api_key:
            raise ValueError(
                "OpenAI API key is required. Set it via OPENAI_API_KEY environment variable or pass it as api_key."
            )
        self.model = model
        self.client = openai.OpenAI(api_key=self.api_key)
        self.token_usage = 0

    def generate_content(self, prompt: str, response_format: Dict[str, str] = None) -> str:
        """
        Generates content using the configured OpenAI model.

        Args:
            prompt (str): The prompt to send to the LLM.
            response_format (Dict[str, str], optional): Specifies the desired response format (e.g., {"type": "json_object"}).
                                                        Defaults to None for plain text.

        Returns:
            str: The generated content from the LLM.
        """
        messages = [{"role": "user", "content": prompt}]
        
        try:
            if response_format:
                response = self.client.chat.completions.create(
                    model=self.model,
                    messages=messages,
                    temperature=0.1,
                    response_format=response_format
                )
            else:
                response = self.client.chat.completions.create(
                    model=self.model,
                    messages=messages,
                    temperature=0.1
                )

            if hasattr(response, 'usage') and response.usage:
                self.token_usage += response.usage.total_tokens

            return response.choices[0].message.content
        except openai.APIError as e:
            print(f"OpenAI API error: {e}")
            raise
        except Exception as e:
            print(f"An unexpected error occurred during LLM content generation: {e}")
            raise

    def get_total_token_usage(self) -> int:
        """
        Returns the total token usage accumulated by this service instance.

        Returns:
            int: Total tokens used.
        """
        return self.token_usage