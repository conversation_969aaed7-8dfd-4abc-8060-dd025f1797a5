import json
import uuid
import re
from typing import Dict, List, Any, Optional, Union
from playwright.sync_api import Page, Locator
from bs4 import BeautifulSoup
from llm_service import LLMService


class AutoPlaywright:
    """
    Python implementation of auto-playwright functionality.
    Allows natural language interaction with web pages using AI.
    Based on lucgagan/auto-playwright but adapted for Python/WindQ.
    """

    def __init__(self, page: Page, llm_service: LLMService, debug: bool = False):
        """
        Initialize Auto Playwright.

        Args:
            page (Page): The Playwright page object
            llm_service (LLMService): LLM service for AI interactions
            debug (bool): Enable debug logging
        """
        self.page = page
        self.llm_service = llm_service
        self.debug = debug
        self.element_cache = {}  # Cache for located elements
        
        # System prompt for the AI
        self.system_prompt = """You must interact with the page using a set of functions.

Locating elements:
- The primary method is to use "locateElement" with a precise CSS selector.
- If you cannot find a reliable unique CSS selector, you can:
  - Use "locateElementsWithText" to find elements by visible text.
  - Use "locateElementsByRole" to find elements by ARIA role (e.g., button, listbox, combobox).

Rules:
1. Prefer locateElement with a specific CSS selector.
2. If a unique selector is not available, prefer locateElementsWithText if the element has unique visible text.
3. If neither CSS selector nor unique text is available, use locateElementsByRole if you know the expected role.
4. You must always locate an element first before performing actions like click, fill, etc.
5. Only the elementId returned by locate functions should be used for further interactions.
6. Never assume you can use a DOM id or text directly as elementId.

If you skip locating an element first, your actions will fail. Always strictly follow this workflow."""

    def auto(self, task: str) -> Union[str, bool, None]:
        """
        Main auto function - executes natural language instructions on the page.
        
        Args:
            task (str): Natural language description of what to do
            
        Returns:
            Union[str, bool, None]: Result depends on task type:
                - Actions return None
                - Queries return string data
                - Assertions return boolean
        """
        if self.debug:
            print(f"[AUTO] Task: {task}")
        
        # Get page snapshot
        snapshot = self._get_page_snapshot()
        
        # Create the prompt
        prompt = self._create_prompt(task, snapshot)
        
        if self.debug:
            print(f"[AUTO] Prompt: {prompt[:500]}...")
        
        # Execute the task using LLM with function calling
        result = self._execute_task_with_llm(prompt)

        if self.debug:
            print(f"[AUTO] Result: {result}")

        return result

    def _create_prompt(self, task: str, snapshot: str) -> str:
        """Create the prompt for the LLM."""
        return f"""This is your task: {task}

* When creating CSS selectors, ensure they are unique and specific enough to select only one element, even if there are multiple elements of the same type (like multiple h1 elements).
* Avoid using generic tags like 'h1' alone. Instead, combine them with other attributes or structural relationships to form a unique selector.
* You must not derive data from the page if you are able to do so by using one of the provided functions, e.g. locator_evaluate.
* After you complete the task, you MUST call one of the result functions:
  - Call resultAction() if you were asked to perform an action (like clicking or selecting an option)
  - Call resultQuery() with the extracted data if you were asked to extract information
  - Call resultAssertion() if you were asked to check or verify something

Webpage snapshot:
```
{snapshot}
```

Available functions:
1. locateElement(cssSelector) - Find element by CSS selector, returns elementId
2. locateElementsWithText(text, exact=false) - Find elements by text content
3. locateElementsByRole(role, exact=false) - Find elements by ARIA role
4. locator_click(elementId) - Click an element
5. locator_fill(elementId, value) - Fill an input field
6. locator_innerText(elementId) - Get element text
7. locator_isVisible(elementId) - Check if element is visible
8. page_goto(url) - Navigate to URL
9. resultAction() - Call when task is an action
10. resultQuery(query) - Call when task asks for data
11. resultAssertion(assertion) - Call when task asks for verification

You must respond with function calls in JSON format:
{{
    "function_calls": [
        {{"name": "function_name", "arguments": {{"param": "value"}}}}
    ]
}}"""

    def _get_page_snapshot(self) -> str:
        """Get a sanitized snapshot of the current page."""
        try:
            # Get the page HTML
            html_content = self.page.content()
            
            # Sanitize HTML to reduce size and improve AI processing
            sanitized_html = self._sanitize_html(html_content)
            
            return sanitized_html
        except Exception as e:
            print(f"Error getting page snapshot: {e}")
            return "<html><body>Error getting page content</body></html>"

    def _sanitize_html(self, html_content: str) -> str:
        """
        Sanitize HTML content to reduce size and focus on important elements.
        Based on auto-playwright's sanitization approach.
        """
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Remove script and style tags
            for tag in soup(['script', 'style', 'meta', 'link']):
                tag.decompose()
            
            # Remove comments
            for comment in soup.find_all(string=lambda text: isinstance(text, str) and text.strip().startswith('<!--')):
                comment.extract()
            
            # Keep only essential attributes
            allowed_attrs = {
                '*': ['id', 'class', 'role', 'aria-label', 'aria-labelledby', 'data-testid'],
                'a': ['href'],
                'img': ['src', 'alt'],
                'input': ['type', 'name', 'placeholder', 'value'],
                'button': ['type'],
                'form': ['action', 'method'],
                'select': ['name'],
                'option': ['value'],
                'textarea': ['name', 'placeholder']
            }
            
            # Clean up attributes
            for tag in soup.find_all():
                # Get allowed attributes for this tag
                tag_attrs = allowed_attrs.get(tag.name, [])
                global_attrs = allowed_attrs.get('*', [])
                allowed = tag_attrs + global_attrs
                
                # Remove unwanted attributes
                attrs_to_remove = []
                for attr in tag.attrs:
                    if attr not in allowed:
                        attrs_to_remove.append(attr)
                
                for attr in attrs_to_remove:
                    del tag.attrs[attr]
            
            # Truncate long text content
            for tag in soup.find_all(string=True):
                if len(tag.strip()) > 100:
                    tag.replace_with(tag[:100] + "...")
            
            return str(soup)
            
        except Exception as e:
            print(f"Error sanitizing HTML: {e}")
            return html_content[:10000]  # Fallback to truncated raw HTML

    def _execute_task_with_llm(self, prompt: str) -> Union[str, bool, None]:
        """Execute the task using LLM with function calling."""
        try:
            # Get LLM response
            response = self.llm_service.get_completion(prompt)
            
            if self.debug:
                print(f"[AUTO] LLM Response: {response}")
            
            # Parse and execute function calls
            return self._execute_function_calls(response)
            
        except Exception as e:
            print(f"Error executing task with LLM: {e}")
            return None

    def _execute_function_calls(self, llm_response: str) -> Union[str, bool, None]:
        """Parse and execute function calls from LLM response."""
        try:
            # Try to parse JSON response
            try:
                response_data = json.loads(llm_response)
                function_calls = response_data.get("function_calls", [])
            except json.JSONDecodeError:
                # Fallback: try to extract JSON from response
                json_match = re.search(r'\{.*\}', llm_response, re.DOTALL)
                if json_match:
                    response_data = json.loads(json_match.group())
                    function_calls = response_data.get("function_calls", [])
                else:
                    print("Could not parse LLM response as JSON")
                    return None
            
            result = None
            
            # Execute each function call
            for call in function_calls:
                func_name = call.get("name")
                func_args = call.get("arguments", {})
                
                if self.debug:
                    print(f"[AUTO] Executing: {func_name}({func_args})")
                
                # Execute the function
                func_result = self._execute_function(func_name, func_args)
                
                # Handle result functions
                if func_name == "resultAction":
                    result = None
                elif func_name == "resultQuery":
                    result = func_args.get("query")
                elif func_name == "resultAssertion":
                    result = func_args.get("assertion")
                
                if self.debug:
                    print(f"[AUTO] Function result: {func_result}")
            
            return result
            
        except Exception as e:
            print(f"Error executing function calls: {e}")
            return None

    def _execute_function(self, func_name: str, func_args: Dict[str, Any]) -> Any:
        """Execute a single function call."""
        try:
            if func_name == "locateElement":
                return self._locate_element(func_args.get("cssSelector"))

            elif func_name == "locateElementsWithText":
                return self._locate_elements_with_text(
                    func_args.get("text"),
                    func_args.get("exact", False)
                )

            elif func_name == "locateElementsByRole":
                return self._locate_elements_by_role(
                    func_args.get("role"),
                    func_args.get("exact", False)
                )

            elif func_name == "locator_click":
                return self._locator_click(func_args.get("elementId"))

            elif func_name == "locator_fill":
                return self._locator_fill(
                    func_args.get("elementId"),
                    func_args.get("value")
                )

            elif func_name == "locator_innerText":
                return self._locator_inner_text(func_args.get("elementId"))

            elif func_name == "locator_isVisible":
                return self._locator_is_visible(func_args.get("elementId"))

            elif func_name == "page_goto":
                return self._page_goto(func_args.get("url"))
            
            elif func_name in ["resultAction", "resultQuery", "resultAssertion"]:
                return {"success": True}
            
            else:
                print(f"Unknown function: {func_name}")
                return {"error": f"Unknown function: {func_name}"}
                
        except Exception as e:
            print(f"Error executing function {func_name}: {e}")
            return {"error": str(e)}

    def _locate_element(self, css_selector: str) -> Dict[str, Any]:
        """Locate element using CSS selector and return elementId."""
        try:
            locator = self.page.locator(css_selector)
            element_id = str(uuid.uuid4())
            
            # Add data attribute to mark the element
            locator.first.evaluate(
                f"(node) => node.setAttribute('data-element-id', '{element_id}')"
            )
            
            # Cache the element
            self.element_cache[element_id] = css_selector
            
            return {"elementId": element_id}
            
        except Exception as e:
            return {"error": f"Could not locate element with selector '{css_selector}': {e}"}

    def _locate_elements_with_text(self, text: str, exact: bool = False) -> Dict[str, Any]:
        """Locate elements containing specified text."""
        try:
            locator = self.page.get_by_text(text, exact=exact)
            visible_locators = []
            
            # Filter for visible elements
            all_locators = locator.all()
            for loc in all_locators:
                if loc.is_visible():
                    visible_locators.append(loc)

            element_ids = []
            for loc in visible_locators:
                element_id = str(uuid.uuid4())
                loc.evaluate(
                    f"(node) => node.setAttribute('data-element-id', '{element_id}')"
                )
                element_ids.append(element_id)
                self.element_cache[element_id] = f"text={text}"
            
            return {"elementIds": element_ids, "count": len(element_ids)}
            
        except Exception as e:
            return {"error": f"Could not locate elements with text '{text}': {e}"}

    def _locate_elements_by_role(self, role: str, exact: bool = False) -> Dict[str, Any]:
        """Locate elements by ARIA role."""
        try:
            locator = self.page.get_by_role(role, exact=exact)
            all_locators = locator.all()

            element_ids = []
            for loc in all_locators:
                element_id = str(uuid.uuid4())
                loc.evaluate(
                    f"(node) => node.setAttribute('data-element-id', '{element_id}')"
                )
                element_ids.append(element_id)
                self.element_cache[element_id] = f"role={role}"
            
            return {"elementIds": element_ids, "count": len(element_ids)}
            
        except Exception as e:
            return {"error": f"Could not locate elements with role '{role}': {e}"}

    def _get_locator(self, element_id: str) -> Locator:
        """Get locator for an element by its ID."""
        return self.page.locator(f'[data-element-id="{element_id}"]')

    def _locator_click(self, element_id: str) -> Dict[str, Any]:
        """Click an element."""
        try:
            locator = self._get_locator(element_id)
            locator.click()
            return {"success": True}
        except Exception as e:
            return {"error": f"Could not click element {element_id}: {e}"}

    def _locator_fill(self, element_id: str, value: str) -> Dict[str, Any]:
        """Fill an input field."""
        try:
            locator = self._get_locator(element_id)
            locator.fill(value)
            return {"success": True}
        except Exception as e:
            return {"error": f"Could not fill element {element_id}: {e}"}

    def _locator_inner_text(self, element_id: str) -> Dict[str, Any]:
        """Get inner text of an element."""
        try:
            locator = self._get_locator(element_id)
            text = locator.inner_text()
            return {"innerText": text}
        except Exception as e:
            return {"error": f"Could not get text from element {element_id}: {e}"}

    def _locator_is_visible(self, element_id: str) -> Dict[str, Any]:
        """Check if element is visible."""
        try:
            locator = self._get_locator(element_id)
            visible = locator.is_visible()
            return {"isVisible": visible}
        except Exception as e:
            return {"error": f"Could not check visibility of element {element_id}: {e}"}

    def _page_goto(self, url: str) -> Dict[str, Any]:
        """Navigate to a URL."""
        try:
            response = self.page.goto(url)
            return {"url": url, "status": response.status if response else None}
        except Exception as e:
            return {"error": f"Could not navigate to {url}: {e}"}
