import json
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable
from playwright.sync_api import Page, Response
from llm_service import LLMService
import os


class AutoTestGenerator:
    """
    Automatically generates test cases by recording user interactions,
    analyzing page behavior, and creating comprehensive test scenarios.
    """

    def __init__(self, page: Page, llm_service: LLMService, output_dir: str = "generated_tests"):
        """
        Initialize the Auto Test Generator.

        Args:
            page (Page): Playwright page instance
            llm_service (LLMService): LLM service for intelligent test generation
            output_dir (str): Directory to save generated tests
        """
        self.page = page
        self.llm_service = llm_service
        self.output_dir = output_dir
        self.recorded_actions = []
        self.page_states = []
        self.network_requests = []
        self.is_recording = False
        self.start_time = None
        
        # Ensure output directory exists
        os.makedirs(output_dir, exist_ok=True)
        
        # Setup event listeners
        self._setup_event_listeners()

    def _setup_event_listeners(self):
        """Setup event listeners to capture page interactions."""
        # Listen to network requests
        self.page.on("request", self._on_request)
        self.page.on("response", self._on_response)
        
        # Listen to console messages
        self.page.on("console", self._on_console)

    def start_recording(self, test_name: str = None):
        """
        Start recording user interactions for test generation.

        Args:
            test_name (str, optional): Name for the test being recorded
        """
        if self.is_recording:
            print("Recording is already in progress")
            return
        
        self.is_recording = True
        self.start_time = time.time()
        self.recorded_actions = []
        self.page_states = []
        self.network_requests = []
        
        test_name = test_name or f"recorded_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        print(f"Started recording test: {test_name}")
        
        # Capture initial page state
        self._capture_page_state("initial_state")

    def stop_recording(self) -> Dict[str, Any]:
        """
        Stop recording and return the captured interactions.

        Returns:
            Dict[str, Any]: Recorded test data
        """
        if not self.is_recording:
            print("No recording in progress")
            return {}
        
        self.is_recording = False
        end_time = time.time()
        duration = end_time - self.start_time
        
        # Capture final page state
        self._capture_page_state("final_state")
        
        recorded_data = {
            "test_metadata": {
                "recorded_at": datetime.now().isoformat(),
                "duration_seconds": duration,
                "total_actions": len(self.recorded_actions),
                "page_url": self.page.url,
                "page_title": self.page.title()
            },
            "recorded_actions": self.recorded_actions,
            "page_states": self.page_states,
            "network_requests": self.network_requests
        }
        
        print(f"Recording stopped. Captured {len(self.recorded_actions)} actions in {duration:.2f} seconds")
        return recorded_data

    def record_action(self, action_type: str, element_info: Dict[str, Any], additional_data: Dict[str, Any] = None):
        """
        Record a user action during test recording.

        Args:
            action_type (str): Type of action (click, fill, navigate, etc.)
            element_info (Dict[str, Any]): Information about the target element
            additional_data (Dict[str, Any], optional): Additional action data
        """
        if not self.is_recording:
            return
        
        timestamp = time.time() - self.start_time
        
        action_record = {
            "timestamp": timestamp,
            "action_type": action_type,
            "element_info": element_info,
            "page_url": self.page.url,
            "additional_data": additional_data or {}
        }
        
        self.recorded_actions.append(action_record)
        print(f"Recorded action: {action_type} at {timestamp:.2f}s")

    def _capture_page_state(self, state_name: str):
        """Capture the current state of the page."""
        try:
            state_data = {
                "name": state_name,
                "timestamp": time.time() - (self.start_time or time.time()),
                "url": self.page.url,
                "title": self.page.title(),
                "html_length": len(self.page.content()),
                "visible_elements": self._count_visible_elements(),
                "form_data": self._extract_form_data(),
                "local_storage": self._get_local_storage(),
                "session_storage": self._get_session_storage()
            }
            
            self.page_states.append(state_data)
        except Exception as e:
            print(f"Error capturing page state: {e}")

    def _count_visible_elements(self) -> Dict[str, int]:
        """Count visible elements on the page."""
        try:
            return {
                "buttons": self.page.locator("button:visible").count(),
                "inputs": self.page.locator("input:visible").count(),
                "links": self.page.locator("a:visible").count(),
                "forms": self.page.locator("form:visible").count(),
                "images": self.page.locator("img:visible").count()
            }
        except Exception:
            return {}

    def _extract_form_data(self) -> List[Dict[str, Any]]:
        """Extract current form data from the page."""
        form_data = []
        try:
            forms = self.page.locator("form").all()
            for i, form in enumerate(forms):
                inputs = form.locator("input, textarea, select").all()
                form_info = {
                    "form_index": i,
                    "action": form.get_attribute("action"),
                    "method": form.get_attribute("method"),
                    "fields": []
                }
                
                for input_elem in inputs:
                    field_info = {
                        "name": input_elem.get_attribute("name"),
                        "type": input_elem.get_attribute("type"),
                        "value": input_elem.input_value() if input_elem.get_attribute("type") != "password" else "[HIDDEN]"
                    }
                    form_info["fields"].append(field_info)
                
                form_data.append(form_info)
        except Exception as e:
            print(f"Error extracting form data: {e}")
        
        return form_data

    def _get_local_storage(self) -> Dict[str, str]:
        """Get local storage data."""
        try:
            return self.page.evaluate("() => Object.assign({}, localStorage)")
        except Exception:
            return {}

    def _get_session_storage(self) -> Dict[str, str]:
        """Get session storage data."""
        try:
            return self.page.evaluate("() => Object.assign({}, sessionStorage)")
        except Exception:
            return {}

    def _on_request(self, request):
        """Handle network request events."""
        if self.is_recording:
            request_data = {
                "timestamp": time.time() - self.start_time,
                "type": "request",
                "url": request.url,
                "method": request.method,
                "headers": dict(request.headers),
                "resource_type": request.resource_type
            }
            self.network_requests.append(request_data)

    def _on_response(self, response: Response):
        """Handle network response events."""
        if self.is_recording:
            response_data = {
                "timestamp": time.time() - self.start_time,
                "type": "response",
                "url": response.url,
                "status": response.status,
                "headers": dict(response.headers),
                "size": len(response.body()) if response.body() else 0
            }
            self.network_requests.append(response_data)

    def _on_console(self, msg):
        """Handle console message events."""
        if self.is_recording and msg.type in ["error", "warning"]:
            console_data = {
                "timestamp": time.time() - self.start_time,
                "type": "console",
                "level": msg.type,
                "text": msg.text,
                "location": msg.location
            }
            self.network_requests.append(console_data)  # Store in network_requests for simplicity

    def generate_test_case_from_recording(self, recorded_data: Dict[str, Any], test_name: str = None) -> Dict[str, Any]:
        """
        Generate a structured test case from recorded interactions.

        Args:
            recorded_data (Dict[str, Any]): Data from stop_recording()
            test_name (str, optional): Name for the generated test

        Returns:
            Dict[str, Any]: Generated test case
        """
        if not test_name:
            test_name = f"generated_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # Use LLM to analyze recorded actions and generate test steps
        actions_summary = self._summarize_recorded_actions(recorded_data["recorded_actions"])
        
        prompt = f"""
        Generate a comprehensive test case from the following recorded user interactions:

        Test Metadata:
        - Duration: {recorded_data['test_metadata']['duration_seconds']:.2f} seconds
        - Total Actions: {recorded_data['test_metadata']['total_actions']}
        - Page: {recorded_data['test_metadata']['page_title']}
        - URL: {recorded_data['test_metadata']['page_url']}

        Recorded Actions Summary:
        {actions_summary}

        Generate a test case with:
        1. Clear test name and description
        2. Structured test steps in JSON format
        3. Expected results and assertions
        4. Test data requirements

        Return a JSON object with this structure:
        {{
            "name": "test_name",
            "description": "what this test validates",
            "steps": [
                {{"action": "goto", "url": "page_url"}},
                {{"action": "fill", "field_description": "field_name", "value": "test_value"}},
                {{"action": "click", "element_description": "button_name"}},
                {{"action": "assert", "assertion_type": "page_title", "expected_value": "expected_title"}}
            ],
            "test_data": {{"key": "value"}},
            "expected_results": ["result1", "result2"],
            "tags": ["tag1", "tag2"]
        }}
        """

        try:
            response = self.llm_service.get_completion(prompt)
            generated_test = json.loads(response)
            
            # Add metadata
            generated_test["generated_from_recording"] = True
            generated_test["recording_metadata"] = recorded_data["test_metadata"]
            generated_test["generated_at"] = datetime.now().isoformat()
            
            return generated_test
            
        except Exception as e:
            print(f"Error generating test case: {e}")
            # Return a basic test case structure
            return {
                "name": test_name,
                "description": "Auto-generated test from recording",
                "steps": self._convert_actions_to_steps(recorded_data["recorded_actions"]),
                "generated_from_recording": True,
                "recording_metadata": recorded_data["test_metadata"],
                "generated_at": datetime.now().isoformat()
            }

    def _summarize_recorded_actions(self, actions: List[Dict[str, Any]]) -> str:
        """Create a summary of recorded actions for LLM analysis."""
        summary_lines = []
        
        for i, action in enumerate(actions):
            timestamp = action.get("timestamp", 0)
            action_type = action.get("action_type", "unknown")
            element_info = action.get("element_info", {})
            
            element_desc = element_info.get("description", element_info.get("text", "element"))
            
            summary_lines.append(f"{i+1}. [{timestamp:.1f}s] {action_type.upper()}: {element_desc}")
        
        return "\n".join(summary_lines)

    def _convert_actions_to_steps(self, actions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Convert recorded actions to test steps format."""
        steps = []
        
        for action in actions:
            action_type = action.get("action_type", "unknown")
            element_info = action.get("element_info", {})
            additional_data = action.get("additional_data", {})
            
            if action_type == "navigate":
                steps.append({
                    "action": "goto",
                    "url": action.get("page_url", "")
                })
            elif action_type == "click":
                steps.append({
                    "action": "click",
                    "element_description": element_info.get("description", "element")
                })
            elif action_type == "fill":
                steps.append({
                    "action": "fill",
                    "field_description": element_info.get("description", "input field"),
                    "value": additional_data.get("value", "")
                })
            elif action_type == "select":
                steps.append({
                    "action": "select",
                    "element_description": element_info.get("description", "dropdown"),
                    "value": additional_data.get("value", "")
                })
        
        return steps

    def save_recorded_data(self, recorded_data: Dict[str, Any], filename: str = None) -> str:
        """
        Save recorded data to a JSON file.

        Args:
            recorded_data (Dict[str, Any]): Data from stop_recording()
            filename (str, optional): Filename for the saved data

        Returns:
            str: Path to the saved file
        """
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"recorded_session_{timestamp}.json"
        
        filepath = os.path.join(self.output_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(recorded_data, f, indent=2, ensure_ascii=False)
        
        print(f"Recorded data saved to: {filepath}")
        return filepath

    def save_generated_test(self, test_case: Dict[str, Any], filename: str = None) -> str:
        """
        Save generated test case to a JSON file.

        Args:
            test_case (Dict[str, Any]): Generated test case
            filename (str, optional): Filename for the saved test

        Returns:
            str: Path to the saved file
        """
        if not filename:
            test_name = test_case.get("name", "generated_test")
            filename = f"{test_name.lower().replace(' ', '_')}.json"
        
        filepath = os.path.join(self.output_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(test_case, f, indent=2, ensure_ascii=False)
        
        print(f"Generated test saved to: {filepath}")
        return filepath
