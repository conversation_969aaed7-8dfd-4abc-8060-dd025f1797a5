{"name": "Auto Playwright De<PERSON> Test", "description": "Demonstrates auto-playwright functionality using natural language commands through the WindQ API", "steps": [{"action": "goto", "url": "https://example.com", "description": "Navigate to example.com"}, {"action": "auto_task", "task_description": "Get the main heading text from the page", "natural_language": "Get the main heading text from the page", "description": "Extract the main heading using auto-playwright"}, {"action": "auto_assert", "assertion_description": "page contains 'Example Domain' text", "natural_language": "Check if the page contains the text 'Example Domain'", "description": "Verify page content using auto-playwright"}, {"action": "auto_task", "task_description": "Check if the 'More information...' link is visible", "natural_language": "Check if the 'More information...' link is visible", "description": "Check element visibility using auto-playwright"}]}