#!/usr/bin/env python3
"""
Test script to verify Azure OpenAI integration.
"""

import os
from dotenv import load_dotenv
from llm_service import LLMService

# Load environment variables
load_dotenv()

def test_azure_openai():
    """Test Azure OpenAI integration."""
    print("🚀 Testing Azure OpenAI Integration")
    print("=" * 50)
    
    # Test 1: Auto-detection
    print("\n📍 Test 1: Auto-detection")
    try:
        llm_service = LLMService()
        print(f"✅ LLM Service initialized successfully")
        print(f"   Using Azure: {llm_service.use_azure}")
        if llm_service.use_azure:
            print(f"   Azure Endpoint: {llm_service.azure_endpoint}")
            print(f"   Azure Deployment: {llm_service.azure_deployment}")
        else:
            print(f"   OpenAI Model: {llm_service.model}")
    except Exception as e:
        print(f"❌ Failed to initialize LLM Service: {e}")
        return
    
    # Test 2: Simple content generation
    print("\n📝 Test 2: Content Generation")
    try:
        prompt = "Say 'Hello from Azure OpenAI!' if you are working correctly."
        response = llm_service.generate_content(prompt)
        print(f"✅ Response received: {response}")
        print(f"   Token usage: {llm_service.get_total_token_usage()}")
    except Exception as e:
        print(f"❌ Failed to generate content: {e}")
        return
    
    # Test 3: JSON response format
    print("\n🔧 Test 3: JSON Response Format")
    try:
        prompt = "Return a JSON object with 'status': 'working' and 'service': 'azure_openai'"
        response = llm_service.generate_content(prompt, response_format={"type": "json_object"})
        print(f"✅ JSON Response: {response}")
        print(f"   Total token usage: {llm_service.get_total_token_usage()}")
    except Exception as e:
        print(f"❌ Failed to generate JSON content: {e}")
        return
    
    print("\n🎉 All tests completed successfully!")

def show_environment_info():
    """Show current environment configuration."""
    print("\n🔍 Environment Configuration:")
    print("-" * 30)
    
    # Check Azure OpenAI environment variables
    azure_endpoint = os.getenv("AZURE_OPENAI_ENDPOINT")
    azure_key = os.getenv("AZURE_OPENAI_API_KEY")
    azure_deployment = os.getenv("AZURE_OPENAI_DEPLOYMENT")
    azure_version = os.getenv("AZURE_OPENAI_API_VERSION")
    
    # Check standard OpenAI environment variables
    openai_key = os.getenv("OPENAI_API_KEY")
    
    print(f"AZURE_OPENAI_ENDPOINT: {'✅ Set' if azure_endpoint else '❌ Not set'}")
    print(f"AZURE_OPENAI_API_KEY: {'✅ Set' if azure_key else '❌ Not set'}")
    print(f"AZURE_OPENAI_DEPLOYMENT: {'✅ Set' if azure_deployment else '❌ Not set'}")
    print(f"AZURE_OPENAI_API_VERSION: {azure_version or 'Default (2024-02-15-preview)'}")
    print(f"OPENAI_API_KEY: {'✅ Set' if openai_key else '❌ Not set'}")
    
    if azure_endpoint and azure_key and azure_deployment:
        print("\n🎯 Will use Azure OpenAI")
    elif openai_key:
        print("\n🎯 Will use standard OpenAI")
    else:
        print("\n❌ No valid API configuration found")

if __name__ == "__main__":
    show_environment_info()
    test_azure_openai()
