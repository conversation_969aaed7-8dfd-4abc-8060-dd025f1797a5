#!/usr/bin/env python3
"""
Test script to demonstrate token usage tracking in WindQ.
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000"

def test_token_usage():
    """Test token usage tracking across different operations."""
    print("🔥 WindQ Token Usage Test")
    print("=" * 50)
    
    # Test 1: Create a test case and track parsing tokens
    print("\n1. 📝 Creating Test Case...")
    test_case = {
        "name": "Token Usage Demo Test",
        "description": "Test case to demonstrate token usage tracking",
        "natural_language_input": "Go to https://example.com, fill the email <NAME_EMAIL>, click the submit button, and verify the dashboard appears"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/create-test-case", json=test_case)
        if response.status_code == 200:
            result = response.json()
            test_id = result["test_id"]
            parsing_tokens = result["tokens_used_for_parsing"]
            
            print(f"✅ Test case created successfully!")
            print(f"   Test ID: {test_id}")
            print(f"   Parsing tokens used: {parsing_tokens}")
            print(f"   Steps generated: {len(result['steps'])}")
            
            # Test 2: Run the test case and track execution tokens
            print(f"\n2. 🚀 Running Test Case...")
            run_response = requests.post(f"{BASE_URL}/run-test-case/{test_id}")
            
            if run_response.status_code == 200:
                run_result = run_response.json()
                execution_tokens = run_result["tokens_used"]
                
                print(f"✅ Test case executed successfully!")
                print(f"   Execution tokens used: {execution_tokens}")
                print(f"   Total tokens for this test: {parsing_tokens + execution_tokens}")
                print(f"   Screenshots saved to: {run_result['screenshots_base_url']}")
                
                # Show step-by-step results
                print(f"\n📊 Step-by-step Results:")
                for i, step_result in enumerate(run_result["results"], 1):
                    status = step_result.get("status", "unknown")
                    action = step_result.get("action", "unknown")
                    print(f"   Step {i}: {action} - {status}")
                
            else:
                print(f"❌ Failed to run test case: {run_response.status_code}")
                print(f"   Error: {run_response.text}")
        
        else:
            print(f"❌ Failed to create test case: {response.status_code}")
            print(f"   Error: {response.text}")
            return
    
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to WindQ server.")
        print("   Make sure the server is running: python main.py")
        return
    except Exception as e:
        print(f"❌ Error: {e}")
        return
    
    # Test 3: Get test case list to see overall statistics
    print(f"\n3. 📈 Getting Test Case List...")
    try:
        list_response = requests.get(f"{BASE_URL}/test-list")
        if list_response.status_code == 200:
            test_list = list_response.json()

            print(f"✅ Test case statistics:")
            print(f"   Total test cases: {len(test_list)}")
            total_runs = sum(test.get('run_count', 0) for test in test_list)
            print(f"   Total executions: {total_runs}")
            print(f"   Current session tokens: {parsing_tokens + execution_tokens}")

            # Show test case details
            for test in test_list[-3:]:  # Show last 3 test cases
                print(f"   • {test['name']}: {test.get('run_count', 0)} runs")
        else:
            print(f"❌ Failed to get test list: {list_response.status_code}")
    except Exception as e:
        print(f"❌ Error getting test list: {e}")
    
    # Test 4: Show how to access token usage in your own code
    print(f"\n4. 💻 Code Examples for Token Usage:")
    print("=" * 50)
    
    print("""
# Example 1: Create test case and get parsing tokens
response = requests.post('http://localhost:8000/create-test-case', json={
    'name': 'My Test',
    'natural_language_input': 'Go to example.com and click login'
})
result = response.json()
parsing_tokens = result['tokens_used_for_parsing']
print(f'Parsing used {parsing_tokens} tokens')

# Example 2: Run test case and get execution tokens  
response = requests.post(f'http://localhost:8000/run-test-case/{test_id}')
result = response.json()
execution_tokens = result['tokens_used']
print(f'Execution used {execution_tokens} tokens')

# Example 3: Get overall statistics
response = requests.get('http://localhost:8000/token-usage-stats')
stats = response.json()
print(f'Total tokens across all operations: {stats["total_tokens"]}')
""")

def show_token_breakdown():
    """Show detailed breakdown of what uses tokens."""
    print(f"\n5. 🔍 Token Usage Breakdown:")
    print("=" * 50)
    
    print("""
📝 PARSING TOKENS (Create/Update Test Case):
   • Converting natural language to structured steps
   • Typical usage: 200-500 tokens per test case
   • Example: "Login and check dashboard" → JSON steps
   
🚀 EXECUTION TOKENS (Run Test Case):
   • Auto-playwright analyzing each webpage
   • LLM deciding how to interact with elements
   • Typical usage: 100-300 tokens per action
   • Example: "Click submit button" → find button → click
   
💡 OPTIMIZATION TIPS:
   • Simpler language = fewer tokens
   • Fewer steps = lower execution cost
   • Reuse test cases instead of recreating
   • Monitor usage with /token-usage-stats endpoint
""")

if __name__ == "__main__":
    test_token_usage()
    show_token_breakdown()
    
    print(f"\n🎯 Next Steps:")
    print("1. Open token_usage_dashboard.html in your browser")
    print("2. Use the /token-usage-stats API endpoint")
    print("3. Check API responses for 'tokens_used' fields")
    print("4. Monitor usage to optimize your test cases")
