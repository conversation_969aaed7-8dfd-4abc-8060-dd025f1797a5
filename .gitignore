# WindQ Backend - Automated Testing Platform
# .gitignore file for Python backend and React frontend

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
windqenv/
venv/
env/
ENV/
.venv/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# React build output
frontend/build/
frontend/dist/

# Test data and screenshots
data/*/
*.png
*.jpg
*.jpeg
*.gif
*.bmp

# Logs
*.log
pip-log.txt

# Testing
.coverage
.pytest_cache/
.cache
htmlcov/
.tox/
.nox/
coverage.xml
*.cover

# IDEs
.idea/
.vscode/
*.sublime-project
*.sublime-workspace

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
*.bak
*.old
*.swp
*.swo
*~

# Playwright
playwright-browsers/

# Applications
*.app
*.exe
*.war

# Large media files
*.mp4
*.tiff
*.avi
*.flv
*.mov
*.wmv

# Test cases (uncomment if you want to ignore test case data)
# test_cases.json