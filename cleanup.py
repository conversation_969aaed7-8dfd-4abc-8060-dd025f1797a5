#!/usr/bin/env python3
"""
Cleanup script for WindQ project.
Removes temporary files, cache files, and old test run data.
"""

import os
import shutil
import glob
from pathlib import Path

def cleanup_project():
    """Clean up unwanted files from the WindQ project."""
    print("🧹 Starting WindQ project cleanup...")
    
    # Files and directories to remove
    cleanup_targets = [
        # Python cache
        "__pycache__",
        "*.pyc",
        "*.pyo", 
        "*.pyd",
        ".Python",
        
        # Test run data (keep data directory structure)
        "data/*-run_*",
        
        # Temporary files
        "*.tmp",
        "*.temp",
        "*.bak",
        "*.old",
        "*.swp",
        "*.swo",
        "*~",
        
        # Log files
        "*.log",
        
        # OS files
        ".DS_Store",
        "Thumbs.db",
        "ehthumbs.db",
        
        # Screenshots (if not needed)
        # "*.png",
        # "*.jpg", 
        # "*.jpeg",
    ]
    
    removed_count = 0
    
    for pattern in cleanup_targets:
        # Handle directory patterns
        if pattern in ["__pycache__"]:
            for root, dirs, files in os.walk("."):
                for dir_name in dirs[:]:  # Use slice to avoid modification during iteration
                    if dir_name == pattern:
                        dir_path = os.path.join(root, dir_name)
                        try:
                            shutil.rmtree(dir_path)
                            print(f"✅ Removed directory: {dir_path}")
                            removed_count += 1
                            dirs.remove(dir_name)  # Don't recurse into removed directory
                        except Exception as e:
                            print(f"❌ Failed to remove {dir_path}: {e}")
        
        # Handle file patterns
        else:
            for file_path in glob.glob(pattern, recursive=True):
                try:
                    if os.path.isfile(file_path):
                        os.remove(file_path)
                        print(f"✅ Removed file: {file_path}")
                        removed_count += 1
                    elif os.path.isdir(file_path):
                        shutil.rmtree(file_path)
                        print(f"✅ Removed directory: {file_path}")
                        removed_count += 1
                except Exception as e:
                    print(f"❌ Failed to remove {file_path}: {e}")
    
    # Clean up empty data subdirectories
    data_dir = Path("data")
    if data_dir.exists():
        for item in data_dir.iterdir():
            if item.is_dir() and not any(item.iterdir()):  # Empty directory
                try:
                    item.rmdir()
                    print(f"✅ Removed empty directory: {item}")
                    removed_count += 1
                except Exception as e:
                    print(f"❌ Failed to remove empty directory {item}: {e}")
    
    print(f"\n🎉 Cleanup completed! Removed {removed_count} items.")
    
    # Show current project size
    try:
        total_size = sum(
            os.path.getsize(os.path.join(dirpath, filename))
            for dirpath, dirnames, filenames in os.walk(".")
            for filename in filenames
        )
        size_mb = total_size / (1024 * 1024)
        print(f"📊 Current project size: {size_mb:.1f} MB")
    except Exception as e:
        print(f"❌ Could not calculate project size: {e}")

def show_large_files(min_size_mb=1):
    """Show files larger than specified size."""
    print(f"\n📁 Files larger than {min_size_mb} MB:")
    large_files = []
    
    for root, dirs, files in os.walk("."):
        # Skip node_modules and virtual environment
        dirs[:] = [d for d in dirs if d not in ['node_modules', 'windqenv', '__pycache__']]
        
        for file in files:
            file_path = os.path.join(root, file)
            try:
                size = os.path.getsize(file_path)
                size_mb = size / (1024 * 1024)
                if size_mb >= min_size_mb:
                    large_files.append((file_path, size_mb))
            except Exception:
                continue
    
    # Sort by size (largest first)
    large_files.sort(key=lambda x: x[1], reverse=True)
    
    if large_files:
        for file_path, size_mb in large_files:
            print(f"  {size_mb:6.1f} MB - {file_path}")
    else:
        print(f"  No files larger than {min_size_mb} MB found.")

if __name__ == "__main__":
    cleanup_project()
    show_large_files()
    
    print("\n💡 Tip: Run this script periodically to keep your project clean!")
    print("💡 Tip: The .gitignore file will prevent most unwanted files from being committed.")
