import random
import string
from typing import Dict, Any
from playwright.sync_api import Page
from auto_playwright import <PERSON><PERSON><PERSON><PERSON>
from llm_service import LLMService

class ActionExecutor:
    """
    Ultra-simplified action executor using auto-playwright.

    Auto-playwright decides what to do based on natural language descriptions.
    No need for specific action types - just describe what you want to do!

    Supported input formats:
    - {"natural_language": "Click the login button"}
    - {"description": "Navigate to Google"}
    - {"url": "https://example.com"} -> "Go to https://example.com"
    - {"field_description": "email", "value": "<EMAIL>"} -> "Fill the email with '<EMAIL>'"
    - {"element_description": "submit button"} -> "Click the submit button"
    """
    def __init__(self, page: Page, llm_service=None):
        """
        Initializes the ActionExecutor.

        Args:
            page (Page): The Playwright page object to interact with.
            llm_service: Optional LLM service instance to reuse (for token tracking)
        """
        self.page = page
        self.llm_service = llm_service
        self.auto_playwright = None  # Initialize on demand

    def _process_special_values(self, value: str) -> str:
        """
        Processes special placeholder values in input strings (e.g., "<EMAIL>").

        Args:
            value (str): The input string value.

        Returns:
            str: The processed string with placeholders replaced.
        """
        if value and (value.lower() == "<EMAIL>" or ("random" in value.lower() and "@" in value)):
            random_prefix = ''.join(random.choices(string.ascii_lowercase, k=8))
            domain = value.split('@')[1] if '@' in value else "example.com"
            return f"{random_prefix}@{domain}"
        return value

    def execute_action(self, action: Dict[str, Any]) -> Dict[str, Any]:
        """
        Executes any action using either direct selectors (if available) or auto-playwright.
        This dramatically reduces token usage for subsequent runs.

        Args:
            action (Dict[str, Any]): A dictionary representing the action to execute.

        Returns:
            Dict[str, Any]: A dictionary containing the status and details of the action execution.
        """
        action_type = action.get("action", "unknown")
        result = {"action": action_type, "status": "failed", "details": {}}

        try:
            # Check if we have a detected selector from previous runs
            detected_selector = action.get("detected_selector")

            if (detected_selector and
                detected_selector.get("strategy") is not None and
                detected_selector.get("value") is not None and
                detected_selector.get("strategy") != "null" and
                detected_selector.get("value") != "null"):

                print(f"🎯 Using cached selector: {detected_selector}")
                direct_result = self._execute_with_direct_selector(action, detected_selector)

                if direct_result.get("success"):
                    result["status"] = "success"
                    result["details"]["method"] = "direct_selector"
                    result["details"]["selector_used"] = detected_selector
                    result["details"]["direct_result"] = direct_result
                    print(f"[ACTION] ✅ Direct selector execution successful")
                    return result
                else:
                    print(f"[ACTION] ⚠️ Direct selector failed: {direct_result.get('error', 'Unknown error')}")
                    print(f"[ACTION] Falling back to auto-playwright")
                    # Fall through to auto-playwright if direct selector fails

            # Use auto-playwright (expensive but intelligent)
            natural_language = self._generate_natural_language(action)

            if natural_language:
                print(f"🤖 Auto-playwright executing: '{natural_language}'")
                auto_result = self._execute_auto_task(natural_language)

                if auto_result.get("success"):
                    result["status"] = "success"
                    result["details"]["method"] = "auto_playwright"
                    result["details"]["natural_language"] = natural_language
                    result["details"]["auto_result"] = auto_result
                    result["details"]["original_action"] = action

                    # Capture detected selector for future runs
                    new_detected_selector = self._extract_detected_selector(auto_result, action)
                    if new_detected_selector:
                        # Update the original action with detected selector
                        action["detected_selector"] = new_detected_selector
                        result["details"]["detected_selector"] = new_detected_selector
                        print(f"[ACTION] 💾 Captured selector for future use: {new_detected_selector}")
                else:
                    result["details"]["error"] = auto_result.get("error", "Auto-playwright execution failed")
                    result["details"]["natural_language"] = natural_language
            else:
                result["details"]["error"] = "Could not generate natural language instruction from action"

        except Exception as e:
            result["status"] = "failed"
            result["details"]["error"] = str(e)
            print(f"Error executing action '{action_type}': {e}")

        return result

    def _execute_with_direct_selector(self, action: Dict[str, Any], detected_selector: Dict[str, str]) -> Dict[str, Any]:
        """
        Execute action using cached selector directly (no LLM calls = 0 tokens).
        Uses dynamic action registry pattern instead of hardcoded action types.

        Args:
            action: The action to execute
            detected_selector: Previously detected selector with strategy and value

        Returns:
            Dict with success status and details
        """
        try:
            action_type = action.get("action", "unknown")
            strategy = detected_selector.get("strategy")
            selector_value = detected_selector.get("value")

            print(f"[DIRECT] Executing {action_type} with {strategy} selector: {selector_value}")

            # Create direct action registry (following createActions pattern)
            direct_actions = self._create_direct_actions()

            if action_type not in direct_actions:
                return {"success": False, "error": f"Direct execution not supported for action: {action_type}"}

            # Execute using dynamic dispatch
            action_handler = direct_actions[action_type]
            return action_handler(action, strategy, selector_value)

        except Exception as e:
            print(f"[DIRECT] Error executing with direct selector: {e}")
            return {"success": False, "error": f"Direct selector execution failed: {e}"}

    def _create_direct_actions(self) -> Dict[str, callable]:
        """
        Automatically discover and register all direct action handlers.
        This follows the true createActions pattern - fully dynamic!

        Any method starting with '_direct_' is automatically registered.
        Action name is derived from method name: _direct_goto -> "goto"
        """
        actions = {}

        # Automatically discover all direct action methods
        for attr_name in dir(self):
            if attr_name.startswith('_direct_') and callable(getattr(self, attr_name)):
                # Extract action name: _direct_goto -> goto
                action_name = attr_name[8:]  # Remove '_direct_' prefix
                action_handler = getattr(self, attr_name)
                actions[action_name] = action_handler

        print(f"[DIRECT] Auto-discovered {len(actions)} direct actions: {list(actions.keys())}")
        return actions

    def _get_locator(self, strategy: str, selector_value: str):
        """Get locator using strategy and selector value with fallback support."""
        if strategy == "css":
            # Handle complex CSS selectors (comma-separated)
            if "," in selector_value:
                # Try each selector until one works
                selectors = [s.strip() for s in selector_value.split(",")]
                for sel in selectors:
                    try:
                        test_locator = self.page.locator(sel)
                        if test_locator.count() > 0:
                            return test_locator
                    except:
                        continue
                return None  # No selector worked
            else:
                return self.page.locator(selector_value)
        elif strategy == "text":
            return self.page.get_by_text(selector_value)
        else:
            raise ValueError(f"Unsupported selector strategy: {strategy}")

    def _direct_goto(self, action: Dict[str, Any], strategy: str, selector_value: str) -> Dict[str, Any]:
        """Direct execution for goto actions."""
        # goto doesn't use selectors, but we keep the signature for consistency
        url = action.get("url")
        if url:
            response = self.page.goto(url)
            return {
                "success": True,
                "url": url,
                "status": response.status if response else 200
            }
        else:
            return {"success": False, "error": "No URL provided for goto action"}

    def _direct_fill(self, action: Dict[str, Any], strategy: str, selector_value: str) -> Dict[str, Any]:
        """Direct execution for fill actions."""
        value = action.get("value", "")
        locator = self._get_locator(strategy, selector_value)

        if not locator or locator.count() == 0:
            return {"success": False, "error": f"Element not found with selector: {selector_value}"}

        locator.first.fill(value)
        return {
            "success": True,
            "action": "fill",
            "selector": selector_value,
            "value": value
        }

    def _direct_click(self, _action: Dict[str, Any], strategy: str, selector_value: str) -> Dict[str, Any]:
        """Direct execution for click actions."""
        locator = self._get_locator(strategy, selector_value)

        if not locator or locator.count() == 0:
            return {"success": False, "error": f"Element not found with selector: {selector_value}"}

        locator.first.click()
        return {
            "success": True,
            "action": "click",
            "selector": selector_value
        }

    def _direct_check(self, _action: Dict[str, Any], strategy: str, selector_value: str) -> Dict[str, Any]:
        """Direct execution for check actions."""
        locator = self._get_locator(strategy, selector_value)

        if not locator or locator.count() == 0:
            return {"success": False, "error": f"Element not found with selector: {selector_value}"}

        locator.first.check()
        return {
            "success": True,
            "action": "check",
            "selector": selector_value
        }

    def _direct_uncheck(self, _action: Dict[str, Any], strategy: str, selector_value: str) -> Dict[str, Any]:
        """Direct execution for uncheck actions."""
        locator = self._get_locator(strategy, selector_value)

        if not locator or locator.count() == 0:
            return {"success": False, "error": f"Element not found with selector: {selector_value}"}

        locator.first.uncheck()
        return {
            "success": True,
            "action": "uncheck",
            "selector": selector_value
        }

    def _direct_select(self, action: Dict[str, Any], strategy: str, selector_value: str) -> Dict[str, Any]:
        """Direct execution for select actions."""
        value = action.get("value", "")
        locator = self._get_locator(strategy, selector_value)

        if not locator or locator.count() == 0:
            return {"success": False, "error": f"Element not found with selector: {selector_value}"}

        locator.first.select_option(value)
        return {
            "success": True,
            "action": "select",
            "selector": selector_value,
            "value": value
        }

    def _direct_hover(self, _action: Dict[str, Any], strategy: str, selector_value: str) -> Dict[str, Any]:
        """Direct execution for hover actions."""
        locator = self._get_locator(strategy, selector_value)

        if not locator or locator.count() == 0:
            return {"success": False, "error": f"Element not found with selector: {selector_value}"}

        locator.first.hover()
        return {
            "success": True,
            "action": "hover",
            "selector": selector_value
        }

    def _direct_wait(self, action: Dict[str, Any], _strategy: str, _selector_value: str) -> Dict[str, Any]:
        """Direct execution for wait actions."""
        time_seconds = action.get("time_seconds", 1)
        import time
        time.sleep(time_seconds)
        return {
            "success": True,
            "action": "wait",
            "time_seconds": time_seconds
        }

    # ========== DYNAMICALLY DISCOVERABLE ACTIONS ==========
    # Any method starting with '_direct_' is automatically registered!
    # No need to manually add to registry - just define the method!

    def _direct_double_click(self, _action: Dict[str, Any], strategy: str, selector_value: str) -> Dict[str, Any]:
        """Direct execution for double-click actions."""
        locator = self._get_locator(strategy, selector_value)

        if not locator or locator.count() == 0:
            return {"success": False, "error": f"Element not found with selector: {selector_value}"}

        locator.first.dblclick()
        return {
            "success": True,
            "action": "double_click",
            "selector": selector_value
        }

    def _direct_right_click(self, _action: Dict[str, Any], strategy: str, selector_value: str) -> Dict[str, Any]:
        """Direct execution for right-click actions."""
        locator = self._get_locator(strategy, selector_value)

        if not locator or locator.count() == 0:
            return {"success": False, "error": f"Element not found with selector: {selector_value}"}

        locator.first.click(button="right")
        return {
            "success": True,
            "action": "right_click",
            "selector": selector_value
        }

    def _direct_clear(self, _action: Dict[str, Any], strategy: str, selector_value: str) -> Dict[str, Any]:
        """Direct execution for clear actions."""
        locator = self._get_locator(strategy, selector_value)

        if not locator or locator.count() == 0:
            return {"success": False, "error": f"Element not found with selector: {selector_value}"}

        locator.first.clear()
        return {
            "success": True,
            "action": "clear",
            "selector": selector_value
        }

    def _direct_focus(self, _action: Dict[str, Any], strategy: str, selector_value: str) -> Dict[str, Any]:
        """Direct execution for focus actions."""
        locator = self._get_locator(strategy, selector_value)

        if not locator or locator.count() == 0:
            return {"success": False, "error": f"Element not found with selector: {selector_value}"}

        locator.first.focus()
        return {
            "success": True,
            "action": "focus",
            "selector": selector_value
        }

    def _direct_scroll_into_view(self, _action: Dict[str, Any], strategy: str, selector_value: str) -> Dict[str, Any]:
        """Direct execution for scroll into view actions."""
        locator = self._get_locator(strategy, selector_value)

        if not locator or locator.count() == 0:
            return {"success": False, "error": f"Element not found with selector: {selector_value}"}

        locator.first.scroll_into_view_if_needed()
        return {
            "success": True,
            "action": "scroll_into_view",
            "selector": selector_value
        }

    def _direct_press_key(self, action: Dict[str, Any], strategy: str, selector_value: str) -> Dict[str, Any]:
        """Direct execution for key press actions."""
        key = action.get("key", "Enter")
        locator = self._get_locator(strategy, selector_value)

        if not locator or locator.count() == 0:
            return {"success": False, "error": f"Element not found with selector: {selector_value}"}

        locator.first.press(key)
        return {
            "success": True,
            "action": "press_key",
            "selector": selector_value,
            "key": key
        }

    def _direct_type_text(self, action: Dict[str, Any], strategy: str, selector_value: str) -> Dict[str, Any]:
        """Direct execution for type text actions (different from fill)."""
        text = action.get("text", "")
        locator = self._get_locator(strategy, selector_value)

        if not locator or locator.count() == 0:
            return {"success": False, "error": f"Element not found with selector: {selector_value}"}

        locator.first.type(text)
        return {
            "success": True,
            "action": "type_text",
            "selector": selector_value,
            "text": text
        }

    def _generate_natural_language(self, action: Dict[str, Any]) -> str:
        """
        Generate natural language instruction from action data.
        FULLY FLEXIBLE - works with any action structure!
        """
        # Priority 1: Explicit instructions (highest priority)
        explicit_instructions = self._extract_value(action, [
            "natural_language", "description", "task_description",
            "instruction", "command", "task", "prompt"
        ], default=None)

        if explicit_instructions:
            return explicit_instructions

        # Priority 2: Try intelligent composition
        return self._compose_intelligent_instruction(action)

    def _compose_intelligent_instruction(self, action: Dict[str, Any]) -> str:
        """
        Intelligently compose natural language from ANY action structure.
        No hardcoded field names - works with any JSON structure!
        """
        action_type = action.get("action", "")

        # Try specific generators first
        natural_language_generators = self._create_natural_language_generators()
        if action_type in natural_language_generators:
            try:
                return natural_language_generators[action_type](action)
            except:
                pass  # Fall through to intelligent composition

        # Intelligent composition for unknown structures
        return self._intelligent_fallback_composition(action)

    def _create_natural_language_generators(self) -> Dict[str, callable]:
        """
        Create natural language generators following createActions pattern.
        Auto-discovers all _nl_* methods (natural language generators).
        """
        generators = {}

        # Auto-discover natural language generator methods
        for attr_name in dir(self):
            if attr_name.startswith('_nl_') and callable(getattr(self, attr_name)):
                # Extract action name: _nl_goto -> goto
                action_name = attr_name[4:]  # Remove '_nl_' prefix
                generators[action_name] = getattr(self, attr_name)

        return generators

    def _nl_goto(self, action: Dict[str, Any]) -> str:
        """Generate natural language for goto actions - fully flexible."""
        url = self._extract_value(action, ["url", "link", "href", "destination"])
        return f"Go to {url}"

    def _nl_fill(self, action: Dict[str, Any]) -> str:
        """Generate natural language for fill actions - fully flexible."""
        field = self._extract_value(action, ["field_description", "element_description", "field", "input", "target"])
        value = self._extract_value(action, ["value", "text", "content", "data"])
        value = self._process_special_values(value)
        return f"Fill the {field} with '{value}'"

    def _nl_click(self, action: Dict[str, Any]) -> str:
        """Generate natural language for click actions - fully flexible."""
        element = self._extract_value(action, ["element_description", "target", "button", "link", "element"])
        return f"Click the {element}"

    def _nl_select(self, action: Dict[str, Any]) -> str:
        """Generate natural language for select actions - fully flexible."""
        element = self._extract_value(action, ["element_description", "dropdown", "select", "menu", "target"])
        value = self._extract_value(action, ["value", "option", "choice", "selection"])
        return f"Select '{value}' from the {element}"

    def _nl_check(self, action: Dict[str, Any]) -> str:
        """Generate natural language for check actions - fully flexible."""
        element = self._extract_value(action, ["element_description", "checkbox", "option", "target"])
        return f"Check the {element}"

    def _nl_uncheck(self, action: Dict[str, Any]) -> str:
        """Generate natural language for uncheck actions - fully flexible."""
        element = self._extract_value(action, ["element_description", "checkbox", "option", "target"])
        return f"Uncheck the {element}"

    def _nl_hover(self, action: Dict[str, Any]) -> str:
        """Generate natural language for hover actions - fully flexible."""
        element = self._extract_value(action, ["element_description", "target", "element", "item"])
        return f"Hover over the {element}"

    def _nl_wait(self, action: Dict[str, Any]) -> str:
        """Generate natural language for wait actions - fully flexible."""
        seconds = self._extract_value(action, ["time_seconds", "seconds", "duration", "wait_time"], default=1)
        return f"Wait for {seconds} seconds"

    def _extract_value(self, action: Dict[str, Any], possible_keys: list, default: str = "element") -> str:
        """
        Flexibly extract values from action dict without hardcoding field names.
        Tries multiple possible key names and returns the first one found.

        Args:
            action: The action dictionary
            possible_keys: List of possible key names to try
            default: Default value if none found

        Returns:
            The extracted value or default
        """
        for key in possible_keys:
            if key in action and action[key] is not None:
                return str(action[key])
        return default

    def _intelligent_fallback_composition(self, action: Dict[str, Any]) -> str:
        """
        ULTIMATE FLEXIBILITY: Compose natural language from ANY action structure.
        Analyzes the action dict and intelligently builds instruction.
        """
        action_type = action.get("action", "perform action")

        # Extract any target/element information
        target = self._find_target_element(action)

        # Extract any value/content information
        value = self._find_value_content(action)

        # Extract any URL/navigation information
        url = self._find_url_navigation(action)

        # Compose based on what we found
        if url:
            return f"Navigate to {url}"
        elif target and value:
            return f"{action_type.title()} the {target} with '{value}'"
        elif target:
            return f"{action_type.title()} the {target}"
        elif value:
            return f"{action_type.title()} with value '{value}'"
        else:
            # Last resort: use all non-system fields
            details = self._extract_meaningful_details(action)
            if details:
                return f"{action_type.title()}: {details}"
            else:
                return f"Perform {action_type}"

    def _find_target_element(self, action: Dict[str, Any]) -> str:
        """Find any field that describes a target element."""
        target_keys = [
            "element_description", "field_description", "target", "element",
            "selector", "locator", "button", "link", "input", "field",
            "checkbox", "dropdown", "menu", "item", "component"
        ]
        return self._extract_value(action, target_keys, default="")

    def _find_value_content(self, action: Dict[str, Any]) -> str:
        """Find any field that contains value/content to use."""
        value_keys = [
            "value", "text", "content", "data", "input_value",
            "option", "choice", "selection", "message"
        ]
        return self._extract_value(action, value_keys, default="")

    def _find_url_navigation(self, action: Dict[str, Any]) -> str:
        """Find any field that contains URL/navigation info."""
        url_keys = [
            "url", "link", "href", "destination", "page", "route", "path"
        ]
        return self._extract_value(action, url_keys, default="")

    def _extract_meaningful_details(self, action: Dict[str, Any]) -> str:
        """Extract meaningful details from action, ignoring system fields."""
        system_fields = {
            "action", "detected_selector", "status", "timestamp",
            "id", "type", "method", "internal_id"
        }

        meaningful_parts = []
        for key, value in action.items():
            if key not in system_fields and value is not None:
                meaningful_parts.append(f"{key}: {value}")

        return ", ".join(meaningful_parts[:3])  # Limit to first 3 meaningful fields

    def _get_auto_playwright(self):
        """Initialize auto-playwright on demand."""
        if self.auto_playwright is None:
            # Use provided LLM service if available, otherwise create new one
            if self.llm_service:
                llm_service = self.llm_service
            else:
                # Use auto-detection for Azure OpenAI vs Standard OpenAI
                llm_service = LLMService(api_key=None)  # Will auto-detect from environment
            self.auto_playwright = AutoPlaywright(self.page, llm_service, debug=False)  # Disable debug to reduce token usage
        return self.auto_playwright

    def _extract_detected_selector(self, auto_result: Dict[str, Any], action: Dict[str, Any]) -> Dict[str, str]:
        """
        Extract the detected selector from auto-playwright result.

        Args:
            auto_result: Result from auto-playwright execution
            action: Original action dictionary

        Returns:
            Dict with strategy and value, or None if no selector detected
        """
        try:
            # Check if auto_result contains explicit selector information from auto-playwright
            if auto_result.get("selector_strategy") and auto_result.get("selector_value"):
                return {
                    "strategy": auto_result["selector_strategy"],
                    "value": auto_result["selector_value"]
                }

            # Check if auto_result contains selector information (legacy format)
            if auto_result.get("selector"):
                return {
                    "strategy": "css",
                    "value": auto_result["selector"]
                }

            # Check if auto_result contains text-based selector
            if auto_result.get("text"):
                return {
                    "strategy": "text",
                    "value": auto_result["text"]
                }

            # For actions that should have selectors, try to infer from action data
            action_type = action.get("action", "")
            actions_needing_selector = ["fill", "click", "wait_for", "select", "hover", "check", "uncheck"]

            if action_type in actions_needing_selector:
                # Try to extract from field_description or element_description
                if action.get("field_description"):
                    # For fill actions, try to generate a likely selector
                    field_desc = action["field_description"].lower()
                    if "email" in field_desc:
                        return {"strategy": "css", "value": "input[name*='email'], input[type='email'], #email"}
                    elif "password" in field_desc:
                        return {"strategy": "css", "value": "input[name*='password'], input[type='password'], #password"}
                    elif "name" in field_desc:
                        return {"strategy": "css", "value": "input[name*='name'], #name"}
                    else:
                        return {"strategy": "css", "value": f"input[name*='{field_desc}'], #{field_desc}"}

                elif action.get("element_description"):
                    element_desc = action["element_description"].lower()
                    if "button" in element_desc or "submit" in element_desc:
                        return {"strategy": "css", "value": "button, input[type='submit']"}
                    elif "link" in element_desc:
                        return {"strategy": "css", "value": "a[href]"}
                    else:
                        return {"strategy": "text", "value": action["element_description"]}

            return None

        except Exception as e:
            print(f"[ACTION] Error extracting detected selector: {e}")
            return None

    def _execute_auto_task(self, task: str) -> Dict[str, Any]:
        """Execute a task using auto-playwright."""
        try:
            print(f"[ACTION] Executing auto-playwright task: '{task}'")
            auto_pw = self._get_auto_playwright()
            result = auto_pw.auto(task)
            print(f"[ACTION] Auto-playwright result: {result}")

            # Check if the result indicates success
            success = result is not None and result is not False

            return {
                "success": success,
                "result": result,
                "task": task
            }
        except Exception as e:
            print(f"[ACTION] Error in auto-playwright task: {e}")
            import traceback
            traceback.print_exc()
            return {
                "success": False,
                "error": str(e),
                "task": task
            }
