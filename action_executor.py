import random
import string
from typing import Dict, Any
from playwright.sync_api import Page
from auto_playwright import <PERSON><PERSON><PERSON><PERSON>
from llm_service import LLMService

class ActionExecutor:
    """
    Ultra-simplified action executor using auto-playwright.

    Auto-playwright decides what to do based on natural language descriptions.
    No need for specific action types - just describe what you want to do!

    Supported input formats:
    - {"natural_language": "Click the login button"}
    - {"description": "Navigate to Google"}
    - {"url": "https://example.com"} -> "Go to https://example.com"
    - {"field_description": "email", "value": "<EMAIL>"} -> "Fill the email with '<EMAIL>'"
    - {"element_description": "submit button"} -> "Click the submit button"
    """
    def __init__(self, page: Page, llm_service=None):
        """
        Initializes the ActionExecutor.

        Args:
            page (Page): The Playwright page object to interact with.
            llm_service: Optional LLM service instance to reuse (for token tracking)
        """
        self.page = page
        self.llm_service = llm_service
        self.auto_playwright = None  # Initialize on demand

    def _process_special_values(self, value: str) -> str:
        """
        Processes special placeholder values in input strings (e.g., "<EMAIL>").

        Args:
            value (str): The input string value.

        Returns:
            str: The processed string with placeholders replaced.
        """
        if value and (value.lower() == "<EMAIL>" or ("random" in value.lower() and "@" in value)):
            random_prefix = ''.join(random.choices(string.ascii_lowercase, k=8))
            domain = value.split('@')[1] if '@' in value else "example.com"
            return f"{random_prefix}@{domain}"
        return value

    def execute_action(self, action: Dict[str, Any]) -> Dict[str, Any]:
        """
        Executes any action using either direct selectors (if available) or auto-playwright.
        This dramatically reduces token usage for subsequent runs.

        Args:
            action (Dict[str, Any]): A dictionary representing the action to execute.

        Returns:
            Dict[str, Any]: A dictionary containing the status and details of the action execution.
        """
        action_type = action.get("action", "unknown")
        result = {"action": action_type, "status": "failed", "details": {}}

        try:
            # Check if we have a detected selector from previous runs
            detected_selector = action.get("detected_selector")

            if (detected_selector and
                detected_selector.get("strategy") is not None and
                detected_selector.get("value") is not None and
                detected_selector.get("strategy") != "null" and
                detected_selector.get("value") != "null"):

                print(f"🎯 Using cached selector: {detected_selector}")
                direct_result = self._execute_with_direct_selector(action, detected_selector)

                if direct_result.get("success"):
                    result["status"] = "success"
                    result["details"]["method"] = "direct_selector"
                    result["details"]["selector_used"] = detected_selector
                    result["details"]["direct_result"] = direct_result
                    print(f"[ACTION] ✅ Direct selector execution successful")
                    return result
                else:
                    print(f"[ACTION] ⚠️ Direct selector failed: {direct_result.get('error', 'Unknown error')}")
                    print(f"[ACTION] Falling back to auto-playwright")
                    # Fall through to auto-playwright if direct selector fails

            # Use auto-playwright (expensive but intelligent)
            natural_language = self._generate_natural_language(action)

            if natural_language:
                print(f"🤖 Auto-playwright executing: '{natural_language}'")
                auto_result = self._execute_auto_task(natural_language)

                if auto_result.get("success"):
                    result["status"] = "success"
                    result["details"]["method"] = "auto_playwright"
                    result["details"]["natural_language"] = natural_language
                    result["details"]["auto_result"] = auto_result
                    result["details"]["original_action"] = action

                    # Capture detected selector for future runs
                    new_detected_selector = self._extract_detected_selector(auto_result, action)
                    if new_detected_selector:
                        # Update the original action with detected selector
                        action["detected_selector"] = new_detected_selector
                        result["details"]["detected_selector"] = new_detected_selector
                        print(f"[ACTION] 💾 Captured selector for future use: {new_detected_selector}")
                else:
                    result["details"]["error"] = auto_result.get("error", "Auto-playwright execution failed")
                    result["details"]["natural_language"] = natural_language
            else:
                result["details"]["error"] = "Could not generate natural language instruction from action"

        except Exception as e:
            result["status"] = "failed"
            result["details"]["error"] = str(e)
            print(f"Error executing action '{action_type}': {e}")

        return result

    def _execute_with_direct_selector(self, action: Dict[str, Any], detected_selector: Dict[str, str]) -> Dict[str, Any]:
        """
        Execute action using cached selector directly (no LLM calls = 0 tokens).

        Args:
            action: The action to execute
            detected_selector: Previously detected selector with strategy and value

        Returns:
            Dict with success status and details
        """
        try:
            action_type = action.get("action", "unknown")
            strategy = detected_selector.get("strategy")
            selector_value = detected_selector.get("value")

            print(f"[DIRECT] Executing {action_type} with {strategy} selector: {selector_value}")

            if action_type == "goto":
                # Navigation doesn't need selectors
                url = action.get("url")
                if url:
                    response = self.page.goto(url)
                    return {
                        "success": True,
                        "url": url,
                        "status": response.status if response else 200
                    }
                else:
                    return {"success": False, "error": "No URL provided for goto action"}

            elif action_type == "fill":
                # Fill input field
                value = action.get("value", "")
                if strategy == "css":
                    # Handle complex CSS selectors (comma-separated)
                    if "," in selector_value:
                        # Try each selector until one works
                        selectors = [s.strip() for s in selector_value.split(",")]
                        locator = None
                        for sel in selectors:
                            try:
                                test_locator = self.page.locator(sel)
                                if test_locator.count() > 0:
                                    locator = test_locator
                                    break
                            except:
                                continue
                        if not locator:
                            return {"success": False, "error": f"None of the CSS selectors found element: {selector_value}"}
                    else:
                        locator = self.page.locator(selector_value)
                elif strategy == "text":
                    locator = self.page.get_by_text(selector_value)
                else:
                    return {"success": False, "error": f"Unsupported selector strategy: {strategy}"}

                if locator.count() == 0:
                    return {"success": False, "error": f"Element not found with selector: {selector_value}"}

                locator.first.fill(value)
                return {
                    "success": True,
                    "action": "fill",
                    "selector": selector_value,
                    "value": value
                }

            elif action_type == "click":
                # Click element
                if strategy == "css":
                    # Handle complex CSS selectors (comma-separated)
                    if "," in selector_value:
                        # Try each selector until one works
                        selectors = [s.strip() for s in selector_value.split(",")]
                        locator = None
                        for sel in selectors:
                            try:
                                test_locator = self.page.locator(sel)
                                if test_locator.count() > 0:
                                    locator = test_locator
                                    break
                            except:
                                continue
                        if not locator:
                            return {"success": False, "error": f"None of the CSS selectors found element: {selector_value}"}
                    else:
                        locator = self.page.locator(selector_value)
                elif strategy == "text":
                    locator = self.page.get_by_text(selector_value)
                else:
                    return {"success": False, "error": f"Unsupported selector strategy: {strategy}"}

                if locator.count() == 0:
                    return {"success": False, "error": f"Element not found with selector: {selector_value}"}

                locator.first.click()
                return {
                    "success": True,
                    "action": "click",
                    "selector": selector_value
                }

            elif action_type == "check":
                # Check checkbox
                if strategy == "css":
                    locator = self.page.locator(selector_value)
                elif strategy == "text":
                    locator = self.page.get_by_text(selector_value)
                else:
                    return {"success": False, "error": f"Unsupported selector strategy: {strategy}"}

                if locator.count() == 0:
                    return {"success": False, "error": f"Element not found with selector: {selector_value}"}

                locator.first.check()
                return {
                    "success": True,
                    "action": "check",
                    "selector": selector_value
                }

            elif action_type == "select":
                # Select from dropdown
                value = action.get("value", "")
                if strategy == "css":
                    locator = self.page.locator(selector_value)
                elif strategy == "text":
                    locator = self.page.get_by_text(selector_value)
                else:
                    return {"success": False, "error": f"Unsupported selector strategy: {strategy}"}

                if locator.count() == 0:
                    return {"success": False, "error": f"Element not found with selector: {selector_value}"}

                locator.first.select_option(value)
                return {
                    "success": True,
                    "action": "select",
                    "selector": selector_value,
                    "value": value
                }

            else:
                return {"success": False, "error": f"Direct selector execution not implemented for action: {action_type}"}

        except Exception as e:
            print(f"[DIRECT] Error executing with direct selector: {e}")
            return {"success": False, "error": f"Direct selector execution failed: {e}"}

    def _generate_natural_language(self, action: Dict[str, Any]) -> str:
        """
        Generate natural language instruction from action data.
        Auto-playwright will decide what to do based on this description.
        """
        action_type = action.get("action", "")

        # If natural_language is explicitly provided, use it
        if action.get("natural_language"):
            return action["natural_language"]

        # If description is provided, use it
        if action.get("description"):
            return action["description"]

        # If task_description is provided, use it
        if action.get("task_description"):
            return action["task_description"]

        # Generate natural language based on action data
        try:
            # Navigation actions
            if action.get("url"):
                return f"Go to {action['url']}"

            # Fill actions
            elif action.get("field_description") and action.get("value") is not None:
                field = action["field_description"]
                value = self._process_special_values(action["value"])
                return f"Fill the {field} with '{value}'"

            # Click actions
            elif action.get("element_description"):
                element = action["element_description"]
                if action_type == "click" or action_type == "auto_click":
                    return f"Click the {element}"
                elif action_type == "hover":
                    return f"Hover over the {element}"
                elif action_type == "check":
                    return f"Check the {element}"
                elif action_type == "uncheck":
                    return f"Uncheck the {element}"
                elif action_type == "wait_for":
                    return f"Wait for the {element} to be visible"
                else:
                    return f"Interact with the {element}"

            # Select actions
            elif action.get("value") and action_type == "select":
                element = action.get("element_description", "dropdown")
                value = action["value"]
                return f"Select '{value}' from the {element}"
            elif action.get("element_description") and action_type == "select":
                element = action["element_description"]
                value = action.get("value", "an option")
                return f"Select '{value}' from the {element}"

            # Wait actions
            elif action.get("time_seconds"):
                seconds = action["time_seconds"]
                return f"Wait for {seconds} seconds"

            # Assertion actions
            elif action.get("assertion_description"):
                assertion = action["assertion_description"]
                return f"Verify that {assertion}"

            # Generic fallback based on action type
            elif action_type:
                return f"Perform {action_type} action"

            # Last resort - try to extract any meaningful text
            else:
                # Look for any text fields that might contain instructions
                for key in ["text", "content", "instruction", "command"]:
                    if action.get(key):
                        return str(action[key])

                return "Perform the requested action"

        except Exception as e:
            print(f"Error generating natural language from action: {e}")
            return f"Perform {action_type} action" if action_type else "Perform the requested action"

    def _get_auto_playwright(self):
        """Initialize auto-playwright on demand."""
        if self.auto_playwright is None:
            # Use provided LLM service if available, otherwise create new one
            if self.llm_service:
                llm_service = self.llm_service
            else:
                # Use auto-detection for Azure OpenAI vs Standard OpenAI
                llm_service = LLMService(api_key=None)  # Will auto-detect from environment
            self.auto_playwright = AutoPlaywright(self.page, llm_service, debug=False)  # Disable debug to reduce token usage
        return self.auto_playwright

    def _extract_detected_selector(self, auto_result: Dict[str, Any], action: Dict[str, Any]) -> Dict[str, str]:
        """
        Extract the detected selector from auto-playwright result.

        Args:
            auto_result: Result from auto-playwright execution
            action: Original action dictionary

        Returns:
            Dict with strategy and value, or None if no selector detected
        """
        try:
            # Check if auto_result contains explicit selector information from auto-playwright
            if auto_result.get("selector_strategy") and auto_result.get("selector_value"):
                return {
                    "strategy": auto_result["selector_strategy"],
                    "value": auto_result["selector_value"]
                }

            # Check if auto_result contains selector information (legacy format)
            if auto_result.get("selector"):
                return {
                    "strategy": "css",
                    "value": auto_result["selector"]
                }

            # Check if auto_result contains text-based selector
            if auto_result.get("text"):
                return {
                    "strategy": "text",
                    "value": auto_result["text"]
                }

            # For actions that should have selectors, try to infer from action data
            action_type = action.get("action", "")
            actions_needing_selector = ["fill", "click", "wait_for", "select", "hover", "check", "uncheck"]

            if action_type in actions_needing_selector:
                # Try to extract from field_description or element_description
                if action.get("field_description"):
                    # For fill actions, try to generate a likely selector
                    field_desc = action["field_description"].lower()
                    if "email" in field_desc:
                        return {"strategy": "css", "value": "input[name*='email'], input[type='email'], #email"}
                    elif "password" in field_desc:
                        return {"strategy": "css", "value": "input[name*='password'], input[type='password'], #password"}
                    elif "name" in field_desc:
                        return {"strategy": "css", "value": "input[name*='name'], #name"}
                    else:
                        return {"strategy": "css", "value": f"input[name*='{field_desc}'], #{field_desc}"}

                elif action.get("element_description"):
                    element_desc = action["element_description"].lower()
                    if "button" in element_desc or "submit" in element_desc:
                        return {"strategy": "css", "value": "button, input[type='submit']"}
                    elif "link" in element_desc:
                        return {"strategy": "css", "value": "a[href]"}
                    else:
                        return {"strategy": "text", "value": action["element_description"]}

            return None

        except Exception as e:
            print(f"[ACTION] Error extracting detected selector: {e}")
            return None

    def _execute_auto_task(self, task: str) -> Dict[str, Any]:
        """Execute a task using auto-playwright."""
        try:
            print(f"[ACTION] Executing auto-playwright task: '{task}'")
            auto_pw = self._get_auto_playwright()
            result = auto_pw.auto(task)
            print(f"[ACTION] Auto-playwright result: {result}")

            # Check if the result indicates success
            success = result is not None and result is not False

            return {
                "success": success,
                "result": result,
                "task": task
            }
        except Exception as e:
            print(f"[ACTION] Error in auto-playwright task: {e}")
            import traceback
            traceback.print_exc()
            return {
                "success": False,
                "error": str(e),
                "task": task
            }
