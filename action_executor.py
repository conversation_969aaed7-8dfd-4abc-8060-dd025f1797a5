import time
import random
import string
from typing import Dict, Any
from playwright.sync_api import Page
from element_finder import ElementFinder

class ActionExecutor:
    """
    Executes a single structured web automation action using Playwright.
    """
    def __init__(self, page: Page, element_finder: ElementFinder):
        """
        Initializes the ActionExecutor.

        Args:
            page (Page): The Playwright page object to interact with.
            element_finder (ElementFinder): An instance of ElementFinder to locate elements.
        """
        self.page = page
        self.element_finder = element_finder

    def _process_special_values(self, value: str) -> str:
        """
        Processes special placeholder values in input strings (e.g., "<EMAIL>").

        Args:
            value (str): The input string value.

        Returns:
            str: The processed string with placeholders replaced.
        """
        if value and (value.lower() == "<EMAIL>" or ("random" in value.lower() and "@" in value)):
            random_prefix = ''.join(random.choices(string.ascii_lowercase, k=8))
            domain = value.split('@')[1] if '@' in value else "example.com"
            return f"{random_prefix}@{domain}"
        return value

    def execute_action(self, action: Dict[str, Any]) -> Dict[str, Any]:
        """
        Executes a single structured automation action.

        Args:
            action (Dict[str, Any]): A dictionary representing the action to execute.
                                     Expected keys: "action", and action-specific parameters.

        Returns:
            Dict[str, Any]: A dictionary containing the status and details of the action execution.
        """
        action_type = action.get("action")
        result = {"action": action_type, "status": "failed", "details": {}}

        try:
            if action_type == "goto":
                url = action.get("url")
                if url:
                    print(f"Navigating to: {url}")
                    self.page.goto(url)
                    result["status"] = "success"
                    result["details"]["url"] = url
                else:
                    result["details"]["error"] = "Missing URL for goto action."

            elif action_type == "fill":
                field_description = action.get("field_description")
                value = action.get("value")
                action_selector = action.get("detected_selector")
                if field_description and value is not None:
                    processed_value = self._process_special_values(value)
                    element, used_selector = self.element_finder.find_element(field_description, detected_selector=action_selector)
                    action["detected_selector"] = used_selector # Update action in-place
                    print(f"Filling '{field_description}' (selector: {used_selector}) with value: '{processed_value}'")
                    element.fill(processed_value)
                    result["status"] = "success"
                    result["details"]["field"] = field_description
                    result["details"]["value"] = processed_value
                    result["details"]["used_selector"] = used_selector
                else:
                    result["details"]["error"] = "Missing field description or value for fill action."

            elif action_type == "click":
                element_description = action.get("element_description")
                action_selector = action.get("detected_selector")
                if element_description:
                    element, used_selector = self.element_finder.find_element(element_description, detected_selector=action_selector)
                    action["detected_selector"] = used_selector # Update action in-place
                    print(f"Clicking: {element_description} (selector: {used_selector})")
                    element.click()
                    result["status"] = "success"
                    result["details"]["element"] = element_description
                    result["details"]["used_selector"] = used_selector
                else:
                    result["details"]["error"] = "Missing element description for click action."

            elif action_type == "wait":
                time_seconds = action.get("time_seconds")
                if isinstance(time_seconds, (int, float)):
                    print(f"Waiting for {time_seconds} seconds.")
                    time.sleep(time_seconds)
                    result["status"] = "success"
                    result["details"]["time_seconds"] = time_seconds
                else:
                    result["details"]["error"] = "Missing or invalid time_seconds for wait action."

            elif action_type == "wait_for":
                element_description = action.get("element_description")
                action_selector = action.get("detected_selector")
                timeout = action.get("timeout", 5) # Convert ms to seconds
                if element_description:
                    print(f"Waiting for element: {element_description} (timeout: {timeout}s)")
                    element, used_selector = self.element_finder.find_element(element_description, detected_selector=action_selector)
                    action["detected_selector"] = used_selector # Update action in-place
                    # Playwright's locator.wait_for() is more robust for waiting for visibility/presence
                    element.wait_for(state="visible", timeout=timeout * 1000) # Convert back to ms
                    result["status"] = "success"
                    result["details"]["element"] = element_description
                    result["details"]["used_selector"] = used_selector
                else:
                    result["details"]["error"] = "Missing element description for wait_for action."

            elif action_type == "select":
                element_description = action.get("element_description")
                value = action.get("value")
                action_selector = action.get("detected_selector")
                if element_description and value is not None:
                    element, used_selector = self.element_finder.find_element(element_description, detected_selector=action_selector)
                    action["detected_selector"] = used_selector # Update action in-place
                    print(f"Selecting '{value}' from '{element_description}' (selector: {used_selector})")
                    element.select_option(value)
                    result["status"] = "success"
                    result["details"]["element"] = element_description
                    result["details"]["value"] = value
                    result["details"]["used_selector"] = used_selector
                else:
                    result["details"]["error"] = "Missing element description or value for select action."

            elif action_type == "hover":
                element_description = action.get("element_description")
                action_selector = action.get("detected_selector")
                if element_description:
                    element, used_selector = self.element_finder.find_element(element_description, detected_selector=action_selector)
                    action["detected_selector"] = used_selector # Update action in-place
                    print(f"Hovering over: {element_description} (selector: {used_selector})")
                    element.hover()
                    result["status"] = "success"
                    result["details"]["element"] = element_description
                    result["details"]["used_selector"] = used_selector
                else:
                    result["details"]["error"] = "Missing element description for hover action."

            elif action_type == "check":
                element_description = action.get("element_description")
                action_selector = action.get("detected_selector")
                if element_description:
                    element, used_selector = self.element_finder.find_element(element_description, detected_selector=action_selector)
                    action["detected_selector"] = used_selector # Update action in-place
                    print(f"Checking: {element_description} (selector: {used_selector})")
                    element.check()
                    result["status"] = "success"
                    result["details"]["element"] = element_description
                    result["details"]["used_selector"] = used_selector
                else:
                    result["details"]["error"] = "Missing element description for check action."

            elif action_type == "uncheck":
                element_description = action.get("element_description")
                action_selector = action.get("detected_selector")
                if element_description:
                    element, used_selector = self.element_finder.find_element(element_description, detected_selector=action_selector)
                    action["detected_selector"] = used_selector # Update action in-place
                    print(f"Unchecking: {element_description} (selector: {used_selector})")
                    element.uncheck()
                    result["status"] = "success"
                    result["details"]["element"] = element_description
                    result["details"]["used_selector"] = used_selector
                else:
                    result["details"]["error"] = "Missing element description for uncheck action."

            else:
                result["details"]["error"] = f"Unknown action type: {action_type}"

        except Exception as e:
            result["status"] = "failed"
            result["details"]["error"] = str(e)
            print(f"Error executing action '{action_type}': {e}")

        return result
