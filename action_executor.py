import time
import random
import string
import os
from typing import Dict, Any
from playwright.sync_api import Page
from element_finder import <PERSON>ement<PERSON><PERSON>
from auto_playwright import <PERSON><PERSON><PERSON><PERSON>
from llm_service import LLMService

class ActionExecutor:
    """
    Executes a single structured web automation action using Playwright.
    """
    def __init__(self, page: Page, element_finder: ElementFinder):
        """
        Initializes the ActionExecutor.

        Args:
            page (Page): The Playwright page object to interact with.
            element_finder (ElementFinder): An instance of ElementFinder to locate elements.
        """
        self.page = page
        self.element_finder = element_finder
        self.auto_playwright = None  # Initialize on demand

    def _process_special_values(self, value: str) -> str:
        """
        Processes special placeholder values in input strings (e.g., "<EMAIL>").

        Args:
            value (str): The input string value.

        Returns:
            str: The processed string with placeholders replaced.
        """
        if value and (value.lower() == "<EMAIL>" or ("random" in value.lower() and "@" in value)):
            random_prefix = ''.join(random.choices(string.ascii_lowercase, k=8))
            domain = value.split('@')[1] if '@' in value else "example.com"
            return f"{random_prefix}@{domain}"
        return value

    def execute_action(self, action: Dict[str, Any]) -> Dict[str, Any]:
        """
        Executes a single structured automation action.

        Args:
            action (Dict[str, Any]): A dictionary representing the action to execute.
                                     Expected keys: "action", and action-specific parameters.

        Returns:
            Dict[str, Any]: A dictionary containing the status and details of the action execution.
        """
        action_type = action.get("action")
        result = {"action": action_type, "status": "failed", "details": {}}

        try:
            if action_type == "goto":
                url = action.get("url")
                if url:
                    print(f"Navigating to: {url}")
                    self.page.goto(url)
                    result["status"] = "success"
                    result["details"]["url"] = url
                else:
                    result["details"]["error"] = "Missing URL for goto action."

            elif action_type == "fill":
                field_description = action.get("field_description")
                value = action.get("value")
                action_selector = action.get("detected_selector")
                if field_description and value is not None:
                    processed_value = self._process_special_values(value)
                    element, used_selector = self.element_finder.find_element(field_description, detected_selector=action_selector)
                    action["detected_selector"] = used_selector # Update action in-place
                    print(f"Filling '{field_description}' (selector: {used_selector}) with value: '{processed_value}'")
                    element.fill(processed_value)
                    result["status"] = "success"
                    result["details"]["field"] = field_description
                    result["details"]["value"] = processed_value
                    result["details"]["used_selector"] = used_selector
                else:
                    result["details"]["error"] = "Missing field description or value for fill action."

            elif action_type == "click":
                element_description = action.get("element_description")
                action_selector = action.get("detected_selector")
                if element_description:
                    element, used_selector = self.element_finder.find_element(element_description, detected_selector=action_selector)
                    action["detected_selector"] = used_selector # Update action in-place
                    print(f"Clicking: {element_description} (selector: {used_selector})")
                    element.click()
                    result["status"] = "success"
                    result["details"]["element"] = element_description
                    result["details"]["used_selector"] = used_selector
                else:
                    result["details"]["error"] = "Missing element description for click action."

            elif action_type == "wait":
                time_seconds = action.get("time_seconds")
                if isinstance(time_seconds, (int, float)):
                    print(f"Waiting for {time_seconds} seconds.")
                    time.sleep(time_seconds)
                    result["status"] = "success"
                    result["details"]["time_seconds"] = time_seconds
                else:
                    result["details"]["error"] = "Missing or invalid time_seconds for wait action."

            elif action_type == "wait_for":
                element_description = action.get("element_description")
                action_selector = action.get("detected_selector")
                timeout = action.get("timeout", 5) # Convert ms to seconds
                if element_description:
                    print(f"Waiting for element: {element_description} (timeout: {timeout}s)")
                    element, used_selector = self.element_finder.find_element(element_description, detected_selector=action_selector)
                    action["detected_selector"] = used_selector # Update action in-place
                    # Playwright's locator.wait_for() is more robust for waiting for visibility/presence
                    element.wait_for(state="visible", timeout=timeout * 1000) # Convert back to ms
                    result["status"] = "success"
                    result["details"]["element"] = element_description
                    result["details"]["used_selector"] = used_selector
                else:
                    result["details"]["error"] = "Missing element description for wait_for action."

            elif action_type == "select":
                element_description = action.get("element_description")
                value = action.get("value")
                action_selector = action.get("detected_selector")
                if element_description and value is not None:
                    element, used_selector = self.element_finder.find_element(element_description, detected_selector=action_selector)
                    action["detected_selector"] = used_selector # Update action in-place
                    print(f"Selecting '{value}' from '{element_description}' (selector: {used_selector})")
                    element.select_option(value)
                    result["status"] = "success"
                    result["details"]["element"] = element_description
                    result["details"]["value"] = value
                    result["details"]["used_selector"] = used_selector
                else:
                    result["details"]["error"] = "Missing element description or value for select action."

            elif action_type == "hover":
                element_description = action.get("element_description")
                action_selector = action.get("detected_selector")
                if element_description:
                    element, used_selector = self.element_finder.find_element(element_description, detected_selector=action_selector)
                    action["detected_selector"] = used_selector # Update action in-place
                    print(f"Hovering over: {element_description} (selector: {used_selector})")
                    element.hover()
                    result["status"] = "success"
                    result["details"]["element"] = element_description
                    result["details"]["used_selector"] = used_selector
                else:
                    result["details"]["error"] = "Missing element description for hover action."

            elif action_type == "check":
                element_description = action.get("element_description")
                action_selector = action.get("detected_selector")
                if element_description:
                    element, used_selector = self.element_finder.find_element(element_description, detected_selector=action_selector)
                    action["detected_selector"] = used_selector # Update action in-place
                    print(f"Checking: {element_description} (selector: {used_selector})")
                    element.check()
                    result["status"] = "success"
                    result["details"]["element"] = element_description
                    result["details"]["used_selector"] = used_selector
                else:
                    result["details"]["error"] = "Missing element description for check action."

            elif action_type == "uncheck":
                element_description = action.get("element_description")
                action_selector = action.get("detected_selector")
                if element_description:
                    element, used_selector = self.element_finder.find_element(element_description, detected_selector=action_selector)
                    action["detected_selector"] = used_selector # Update action in-place
                    print(f"Unchecking: {element_description} (selector: {used_selector})")
                    element.uncheck()
                    result["status"] = "success"
                    result["details"]["element"] = element_description
                    result["details"]["used_selector"] = used_selector
                else:
                    result["details"]["error"] = "Missing element description for uncheck action."

            # Auto-playwright actions
            elif action_type == "auto_click":
                element_description = action.get("element_description")
                natural_language = action.get("natural_language", f"Click {element_description}")
                if element_description:
                    auto_result = self._execute_auto_task(natural_language)
                    if auto_result.get("success"):
                        result["status"] = "success"
                        result["details"]["element"] = element_description
                        result["details"]["auto_result"] = auto_result
                    else:
                        result["details"]["error"] = auto_result.get("error", "Auto-click failed")
                else:
                    result["details"]["error"] = "Missing element description for auto_click action."

            elif action_type == "auto_fill":
                field_description = action.get("field_description")
                value = action.get("value")
                natural_language = action.get("natural_language", f"Fill {field_description} with {value}")
                if field_description and value is not None:
                    processed_value = self._process_special_values(value)
                    auto_result = self._execute_auto_task(natural_language.replace(str(value), processed_value))
                    if auto_result.get("success"):
                        result["status"] = "success"
                        result["details"]["field"] = field_description
                        result["details"]["value"] = processed_value
                        result["details"]["auto_result"] = auto_result
                    else:
                        result["details"]["error"] = auto_result.get("error", "Auto-fill failed")
                else:
                    result["details"]["error"] = "Missing field description or value for auto_fill action."

            elif action_type == "auto_assert":
                assertion_description = action.get("assertion_description")
                natural_language = action.get("natural_language", f"Verify {assertion_description}")
                if assertion_description:
                    auto_result = self._execute_auto_task(natural_language)
                    if auto_result.get("success"):
                        result["status"] = "success"
                        result["details"]["assertion"] = assertion_description
                        result["details"]["auto_result"] = auto_result
                        result["details"]["assertion_passed"] = auto_result.get("result", False)
                    else:
                        result["details"]["error"] = auto_result.get("error", "Auto-assertion failed")
                else:
                    result["details"]["error"] = "Missing assertion description for auto_assert action."

            elif action_type == "auto_task":
                task_description = action.get("task_description")
                natural_language = action.get("natural_language", task_description)
                if task_description:
                    auto_result = self._execute_auto_task(natural_language)
                    if auto_result.get("success"):
                        result["status"] = "success"
                        result["details"]["task"] = task_description
                        result["details"]["auto_result"] = auto_result
                    else:
                        result["details"]["error"] = auto_result.get("error", "Auto-task failed")
                else:
                    result["details"]["error"] = "Missing task description for auto_task action."

            else:
                result["details"]["error"] = f"Unknown action type: {action_type}"

        except Exception as e:
            result["status"] = "failed"
            result["details"]["error"] = str(e)
            print(f"Error executing action '{action_type}': {e}")

        return result

    def _get_auto_playwright(self):
        """Initialize auto-playwright on demand."""
        if self.auto_playwright is None:
            llm_service = LLMService(api_key=os.getenv("OPENAI_API_KEY"))
            self.auto_playwright = AutoPlaywright(self.page, llm_service, debug=False)
        return self.auto_playwright

    def _execute_auto_task(self, task: str) -> Dict[str, Any]:
        """Execute a task using auto-playwright."""
        try:
            auto_pw = self._get_auto_playwright()
            result = auto_pw.auto(task)
            return {
                "success": True,
                "result": result,
                "task": task
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "task": task
            }
