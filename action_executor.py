import random
import string
import os
from typing import Dict, Any
from playwright.sync_api import Page
from auto_playwright import <PERSON><PERSON><PERSON><PERSON>
from llm_service import LLMService

class ActionExecutor:
    """
    Ultra-simplified action executor using auto-playwright.

    Auto-playwright decides what to do based on natural language descriptions.
    No need for specific action types - just describe what you want to do!

    Supported input formats:
    - {"natural_language": "Click the login button"}
    - {"description": "Navigate to Google"}
    - {"url": "https://example.com"} -> "Go to https://example.com"
    - {"field_description": "email", "value": "<EMAIL>"} -> "Fill the email with '<EMAIL>'"
    - {"element_description": "submit button"} -> "Click the submit button"
    """
    def __init__(self, page: Page):
        """
        Initializes the ActionExecutor.

        Args:
            page (Page): The Playwright page object to interact with.
        """
        self.page = page
        self.auto_playwright = None  # Initialize on demand

    def _process_special_values(self, value: str) -> str:
        """
        Processes special placeholder values in input strings (e.g., "<EMAIL>").

        Args:
            value (str): The input string value.

        Returns:
            str: The processed string with placeholders replaced.
        """
        if value and (value.lower() == "<EMAIL>" or ("random" in value.lower() and "@" in value)):
            random_prefix = ''.join(random.choices(string.ascii_lowercase, k=8))
            domain = value.split('@')[1] if '@' in value else "example.com"
            return f"{random_prefix}@{domain}"
        return value

    def execute_action(self, action: Dict[str, Any]) -> Dict[str, Any]:
        """
        Executes any action using auto-playwright's natural language understanding.
        Auto-playwright decides what to do based on the natural language description.

        Args:
            action (Dict[str, Any]): A dictionary representing the action to execute.

        Returns:
            Dict[str, Any]: A dictionary containing the status and details of the action execution.
        """
        action_type = action.get("action", "unknown")
        result = {"action": action_type, "status": "failed", "details": {}}

        try:
            # Generate natural language instruction from action data
            natural_language = self._generate_natural_language(action)

            if natural_language:
                print(f"🤖 Auto-playwright executing: '{natural_language}'")
                auto_result = self._execute_auto_task(natural_language)

                if auto_result.get("success"):
                    result["status"] = "success"
                    result["details"]["natural_language"] = natural_language
                    result["details"]["auto_result"] = auto_result
                    result["details"]["original_action"] = action
                else:
                    result["details"]["error"] = auto_result.get("error", "Auto-playwright execution failed")
                    result["details"]["natural_language"] = natural_language
            else:
                result["details"]["error"] = "Could not generate natural language instruction from action"

        except Exception as e:
            result["status"] = "failed"
            result["details"]["error"] = str(e)
            print(f"Error executing action '{action_type}': {e}")

        return result

    def _generate_natural_language(self, action: Dict[str, Any]) -> str:
        """
        Generate natural language instruction from action data.
        Auto-playwright will decide what to do based on this description.
        """
        action_type = action.get("action", "")

        # If natural_language is explicitly provided, use it
        if action.get("natural_language"):
            return action["natural_language"]

        # If description is provided, use it
        if action.get("description"):
            return action["description"]

        # If task_description is provided, use it
        if action.get("task_description"):
            return action["task_description"]

        # Generate natural language based on action data
        try:
            # Navigation actions
            if action.get("url"):
                return f"Go to {action['url']}"

            # Fill actions
            elif action.get("field_description") and action.get("value") is not None:
                field = action["field_description"]
                value = self._process_special_values(action["value"])
                return f"Fill the {field} with '{value}'"

            # Click actions
            elif action.get("element_description"):
                element = action["element_description"]
                if action_type == "click" or action_type == "auto_click":
                    return f"Click the {element}"
                elif action_type == "hover":
                    return f"Hover over the {element}"
                elif action_type == "check":
                    return f"Check the {element}"
                elif action_type == "uncheck":
                    return f"Uncheck the {element}"
                elif action_type == "wait_for":
                    return f"Wait for the {element} to be visible"
                else:
                    return f"Interact with the {element}"

            # Select actions
            elif action.get("value") and action_type == "select":
                element = action.get("element_description", "dropdown")
                value = action["value"]
                return f"Select '{value}' from the {element}"
            elif action.get("element_description") and action_type == "select":
                element = action["element_description"]
                value = action.get("value", "an option")
                return f"Select '{value}' from the {element}"

            # Wait actions
            elif action.get("time_seconds"):
                seconds = action["time_seconds"]
                return f"Wait for {seconds} seconds"

            # Assertion actions
            elif action.get("assertion_description"):
                assertion = action["assertion_description"]
                return f"Verify that {assertion}"

            # Generic fallback based on action type
            elif action_type:
                return f"Perform {action_type} action"

            # Last resort - try to extract any meaningful text
            else:
                # Look for any text fields that might contain instructions
                for key in ["text", "content", "instruction", "command"]:
                    if action.get(key):
                        return str(action[key])

                return "Perform the requested action"

        except Exception as e:
            print(f"Error generating natural language from action: {e}")
            return f"Perform {action_type} action" if action_type else "Perform the requested action"

    def _get_auto_playwright(self):
        """Initialize auto-playwright on demand."""
        if self.auto_playwright is None:
            llm_service = LLMService(api_key=os.getenv("OPENAI_API_KEY"))
            self.auto_playwright = AutoPlaywright(self.page, llm_service, debug=True)  # Enable debug for troubleshooting
        return self.auto_playwright

    def _execute_auto_task(self, task: str) -> Dict[str, Any]:
        """Execute a task using auto-playwright."""
        try:
            print(f"[ACTION] Executing auto-playwright task: '{task}'")
            auto_pw = self._get_auto_playwright()
            result = auto_pw.auto(task)
            print(f"[ACTION] Auto-playwright result: {result}")

            # Check if the result indicates success
            success = result is not None and result is not False

            return {
                "success": success,
                "result": result,
                "task": task
            }
        except Exception as e:
            print(f"[ACTION] Error in auto-playwright task: {e}")
            import traceback
            traceback.print_exc()
            return {
                "success": False,
                "error": str(e),
                "task": task
            }
